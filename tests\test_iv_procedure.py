"""
Test script for I-V curve procedure.

This script demonstrates the automated I-V curve measurement
capabilities of the laboratory automation framework.
"""

import sys
import os
import time
import logging

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from instruments.base import MockAdapter
from instruments.keithley2400 import Keithley2400
from procedures.iv_curve import IVCurveProcedure
from procedures.base import ProcedureState

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_mock_smu():
    """Create a mock SMU with realistic I-V responses."""
    mock_adapter = MockAdapter("MOCK::KEITHLEY2400::INSTR")

    # State tracking for the mock adapter
    mock_state = {
        'voltage': 0.0,
        'current_limit': 0.001,
        'output_enabled': False
    }

    # Add basic responses
    mock_adapter.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39")
    mock_adapter.add_response(":SENS:CURR:PROT?", "1.050000E-03")
    mock_adapter.add_response(":SOUR:FUNC?", "VOLT")

    # Override the query method to handle dynamic responses
    original_query = mock_adapter.query

    def dynamic_query(command):
        if command == ":SOUR:VOLT:LEV?":
            return f"{mock_state['voltage']:.6f}"
        elif command == ":OUTP?":
            return "1" if mock_state['output_enabled'] else "0"
        else:
            return original_query(command)

    # Override the write method to track state changes
    original_write = mock_adapter.write

    def dynamic_write(command):
        if command.startswith(":SOUR:VOLT:LEV"):
            # Extract voltage value
            try:
                voltage_str = command.split()[-1]
                mock_state['voltage'] = float(voltage_str)
            except:
                pass
        elif command.startswith(":SENS:CURR:PROT"):
            # Extract current limit
            try:
                current_str = command.split()[-1]
                mock_state['current_limit'] = float(current_str)
            except:
                pass
        elif command == ":OUTP ON":
            mock_state['output_enabled'] = True
        elif command == ":OUTP OFF":
            mock_state['output_enabled'] = False

        original_write(command)

    mock_adapter.query = dynamic_query
    mock_adapter.write = dynamic_write

    # Create SMU
    smu = Keithley2400(mock_adapter, name="Mock SMU")

    # Override the measure_iv method to simulate realistic I-V behavior
    def mock_measure_iv():
        """Simulate I-V measurement with Ohm's law + noise."""
        voltage = mock_state['voltage']
        resistance = 1000.0  # 1kΩ resistor simulation

        # Only measure if output is enabled
        if not mock_state['output_enabled']:
            return 0.0, 0.0

        # Calculate basic current
        current = voltage / resistance

        # Add some nonlinearity and noise
        current += 0.001 * voltage**2  # Quadratic nonlinearity

        # Add noise (avoid using hash for reproducibility)
        import random
        random.seed(int(abs(voltage) * 1000) + 42)  # Deterministic noise based on voltage
        noise = 0.0001 * (random.random() - 0.5) * max(abs(voltage), 0.1)
        current += noise

        # Apply current limiting
        if abs(current) > mock_state['current_limit']:
            current = mock_state['current_limit'] if current > 0 else -mock_state['current_limit']

        # Ensure current is not exactly zero to avoid division by zero
        if abs(current) < 1e-12:
            current = 1e-12 if voltage >= 0 else -1e-12

        return voltage, current

    smu.measure_iv = mock_measure_iv

    return smu


def test_basic_iv_procedure():
    """Test basic I-V curve procedure execution."""
    print("\n" + "="*50)
    print("Basic I-V Curve Procedure Test")
    print("="*50)
    
    # Create mock SMU
    smu = create_mock_smu()
    
    # Create I-V procedure
    iv_procedure = IVCurveProcedure()
    
    # Setup progress tracking
    def progress_callback(progress):
        print(f"Progress: {progress:.1f}%")
    
    def data_callback(data_point):
        if 'voltage' in data_point.measurements:
            v = data_point.measurements['voltage']
            i = data_point.measurements['current']
            r = data_point.measurements.get('resistance', 'N/A')
            print(f"  Step {data_point.step}: V={v:.3f}V, I={i:.6f}A, R={r:.1f}Ω" if r != 'N/A' else f"  Step {data_point.step}: {data_point.parameters.get('action', 'measurement')}")
    
    def state_callback(state):
        print(f"State changed to: {state.value}")
    
    iv_procedure.add_progress_callback(progress_callback)
    iv_procedure.add_data_callback(data_callback)
    iv_procedure.add_state_callback(state_callback)
    
    # Configure and start the procedure
    print("\nStarting I-V curve measurement...")
    print("Parameters: -1V to +1V, 11 points, 10mA limit")
    
    iv_procedure.start(
        smu=smu,
        start_voltage=-1.0,
        stop_voltage=1.0,
        num_points=11,
        current_limit=0.01,
        settling_time=0.05,
        measurement_delay=0.02
    )
    
    # Wait for completion
    result = iv_procedure.wait_for_completion(timeout=30)
    
    print(f"\nProcedure completed with state: {result.state.value}")
    print(f"Success: {result.success}")
    print(f"Duration: {result.duration:.2f}s" if result.duration else "Duration: N/A")
    print(f"Data points collected: {len(result.data_points)}")
    
    if result.error_message:
        print(f"Error: {result.error_message}")
    
    # Analyze results
    if result.success:
        analysis = iv_procedure.analyze_results()
        print(f"\nAnalysis Results:")
        print(f"  Voltage range: {analysis.get('voltage_range', 'N/A')}")
        print(f"  Current range: {analysis.get('current_range', 'N/A')}")
        print(f"  Average resistance: {analysis.get('avg_resistance', 'N/A'):.1f}Ω" if 'avg_resistance' in analysis else "  Average resistance: N/A")
        print(f"  Max power: {analysis.get('max_power', 'N/A'):.6f}W" if 'max_power' in analysis else "  Max power: N/A")
        print(f"  Compliance events: {analysis.get('compliance_events', 0)}")
        
        # Save data
        iv_procedure.save_data("test_iv_curve.csv")
        iv_procedure.save_data("test_iv_curve.json")
        print(f"\nData saved to test_iv_curve.csv and test_iv_curve.json")
    
    # Cleanup
    smu.close()
    
    return result


def test_procedure_control():
    """Test procedure control (pause, resume, stop)."""
    print("\n" + "="*50)
    print("Procedure Control Test")
    print("="*50)
    
    # Create mock SMU
    smu = create_mock_smu()
    
    # Create I-V procedure with more points for longer execution
    iv_procedure = IVCurveProcedure()
    
    # Setup callbacks
    def progress_callback(progress):
        print(f"Progress: {progress:.1f}%")
        
        # Pause at 30% progress
        if 25 < progress < 35 and iv_procedure.state == ProcedureState.RUNNING:
            print("  -> Pausing procedure...")
            iv_procedure.pause()
    
    def state_callback(state):
        print(f"State: {state.value}")
        
        # Resume after 2 seconds if paused
        if state == ProcedureState.PAUSED:
            def resume_after_delay():
                time.sleep(2)
                if iv_procedure.state == ProcedureState.PAUSED:
                    print("  -> Resuming procedure...")
                    iv_procedure.resume()
            
            import threading
            threading.Thread(target=resume_after_delay, daemon=True).start()
    
    iv_procedure.add_progress_callback(progress_callback)
    iv_procedure.add_state_callback(state_callback)
    
    # Start procedure
    print("Starting procedure with pause/resume test...")
    iv_procedure.start(
        smu=smu,
        start_voltage=-2.0,
        stop_voltage=2.0,
        num_points=21,
        current_limit=0.01,
        settling_time=0.1
    )
    
    # Wait for completion
    result = iv_procedure.wait_for_completion(timeout=60)
    
    print(f"\nProcedure completed: {result.success}")
    print(f"Final state: {result.state.value}")
    
    # Cleanup
    smu.close()
    
    return result


def test_procedure_cancellation():
    """Test procedure cancellation."""
    print("\n" + "="*50)
    print("Procedure Cancellation Test")
    print("="*50)
    
    # Create mock SMU
    smu = create_mock_smu()
    
    # Create I-V procedure
    iv_procedure = IVCurveProcedure()
    
    # Setup callbacks
    def progress_callback(progress):
        print(f"Progress: {progress:.1f}%")
        
        # Cancel at 50% progress
        if 45 < progress < 55:
            print("  -> Cancelling procedure...")
            iv_procedure.stop()
    
    def state_callback(state):
        print(f"State: {state.value}")
    
    iv_procedure.add_progress_callback(progress_callback)
    iv_procedure.add_state_callback(state_callback)
    
    # Start procedure
    print("Starting procedure with cancellation test...")
    iv_procedure.start(
        smu=smu,
        start_voltage=-1.0,
        stop_voltage=1.0,
        num_points=21,
        current_limit=0.01,
        settling_time=0.1
    )
    
    # Wait for completion
    result = iv_procedure.wait_for_completion(timeout=30)
    
    print(f"\nProcedure result: {result.success}")
    print(f"Final state: {result.state.value}")
    print(f"Data points before cancellation: {len(result.data_points)}")
    
    # Cleanup
    smu.close()
    
    return result


def interactive_menu():
    """Provide an interactive menu for testing procedures."""
    while True:
        print("\n" + "="*50)
        print("I-V Curve Procedure Test Menu")
        print("="*50)
        print("1. Basic I-V curve test")
        print("2. Procedure control test (pause/resume)")
        print("3. Procedure cancellation test")
        print("4. Run all tests")
        print("5. Exit")
        
        try:
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                test_basic_iv_procedure()
            elif choice == "2":
                test_procedure_control()
            elif choice == "3":
                test_procedure_cancellation()
            elif choice == "4":
                test_basic_iv_procedure()
                test_procedure_control()
                test_procedure_cancellation()
            elif choice == "5":
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1, 2, 3, 4, or 5.")
                
        except KeyboardInterrupt:
            print("\n\nExiting...")
            break
        except Exception as e:
            logger.error(f"Error in interactive menu: {e}")
            print(f"An error occurred: {e}")


if __name__ == "__main__":
    try:
        if len(sys.argv) > 1:
            if sys.argv[1] == "basic":
                test_basic_iv_procedure()
            elif sys.argv[1] == "control":
                test_procedure_control()
            elif sys.argv[1] == "cancel":
                test_procedure_cancellation()
            elif sys.argv[1] == "all":
                test_basic_iv_procedure()
                test_procedure_control()
                test_procedure_cancellation()
            else:
                print("Usage: python test_iv_procedure.py [basic|control|cancel|all]")
        else:
            interactive_menu()
            
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
        sys.exit(1)
