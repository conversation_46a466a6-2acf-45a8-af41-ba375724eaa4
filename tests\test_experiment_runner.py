"""
Test script for multi-threaded experiment runner.

This script demonstrates the multi-threaded experiment execution
capabilities with real-time plotting and GUI integration.
"""

import sys
import os
import time
import logging
import tkinter as tk
from tkinter import ttk

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from instruments.base import MockAdapter
from instruments.keithley2400 import Keithley2400
from procedures.iv_curve import IVCurveProcedure
from experiment_runner import ExperimentRunner, ExperimentState
from gui.widgets.plotting import RealTimePlotWidget, MultiChannelPlotWidget

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_mock_smu():
    """Create a mock SMU with realistic I-V responses."""
    mock_adapter = MockAdapter("MOCK::KEITHLEY2400::INSTR")
    
    # State tracking for the mock adapter
    mock_state = {
        'voltage': 0.0,
        'current_limit': 0.001,
        'output_enabled': False
    }
    
    # Add basic responses
    mock_adapter.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39")
    mock_adapter.add_response(":SENS:CURR:PROT?", "1.050000E-03")
    mock_adapter.add_response(":SOUR:FUNC?", "VOLT")
    
    # Override the query method to handle dynamic responses
    original_query = mock_adapter.query
    
    def dynamic_query(command):
        if command == ":SOUR:VOLT:LEV?":
            return f"{mock_state['voltage']:.6f}"
        elif command == ":OUTP?":
            return "1" if mock_state['output_enabled'] else "0"
        else:
            return original_query(command)
    
    # Override the write method to track state changes
    original_write = mock_adapter.write
    
    def dynamic_write(command):
        if command.startswith(":SOUR:VOLT:LEV"):
            try:
                voltage_str = command.split()[-1]
                mock_state['voltage'] = float(voltage_str)
            except:
                pass
        elif command.startswith(":SENS:CURR:PROT"):
            try:
                current_str = command.split()[-1]
                mock_state['current_limit'] = float(current_str)
            except:
                pass
        elif command == ":OUTP ON":
            mock_state['output_enabled'] = True
        elif command == ":OUTP OFF":
            mock_state['output_enabled'] = False
        
        original_write(command)
    
    mock_adapter.query = dynamic_query
    mock_adapter.write = dynamic_write
    
    # Create SMU
    smu = Keithley2400(mock_adapter, name="Mock SMU")
    
    # Override the measure_iv method to simulate realistic I-V behavior
    def mock_measure_iv():
        """Simulate I-V measurement with Ohm's law + noise."""
        voltage = mock_state['voltage']
        resistance = 1000.0  # 1kΩ resistor simulation
        
        # Only measure if output is enabled
        if not mock_state['output_enabled']:
            return 0.0, 0.0
        
        # Calculate basic current
        current = voltage / resistance
        
        # Add some nonlinearity and noise
        current += 0.001 * voltage**2  # Quadratic nonlinearity
        
        # Add noise
        import random
        random.seed(int(abs(voltage) * 1000) + 42)
        noise = 0.0001 * (random.random() - 0.5) * max(abs(voltage), 0.1)
        current += noise
        
        # Apply current limiting
        if abs(current) > mock_state['current_limit']:
            current = mock_state['current_limit'] if current > 0 else -mock_state['current_limit']
        
        # Ensure current is not exactly zero
        if abs(current) < 1e-12:
            current = 1e-12 if voltage >= 0 else -1e-12
        
        return voltage, current
    
    smu.measure_iv = mock_measure_iv
    
    return smu


class ExperimentRunnerGUI:
    """GUI application for testing the experiment runner."""
    
    def __init__(self):
        """Initialize the GUI application."""
        self.root = tk.Tk()
        self.root.title("Multi-threaded Experiment Runner Demo")
        self.root.geometry("1200x800")
        
        # Create experiment runner and instruments
        self.experiment_runner = ExperimentRunner()
        self.smu = create_mock_smu()
        
        # Setup GUI
        self._setup_gui()
        self._setup_callbacks()
        
        # Update timer
        self._schedule_update()
    
    def _setup_gui(self):
        """Setup the GUI layout."""
        # Create main paned window
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel for controls
        self.control_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.control_frame, weight=1)
        
        # Right panel for plotting
        self.plot_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.plot_frame, weight=2)
        
        # Setup control panel
        self._setup_control_panel()
        
        # Setup plotting area
        self._setup_plotting_area()
    
    def _setup_control_panel(self):
        """Setup the control panel."""
        # Experiment parameters
        params_frame = ttk.LabelFrame(self.control_frame, text="Experiment Parameters")
        params_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Start voltage
        ttk.Label(params_frame, text="Start Voltage (V):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.start_voltage_var = tk.StringVar(value="-1.0")
        ttk.Entry(params_frame, textvariable=self.start_voltage_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        # Stop voltage
        ttk.Label(params_frame, text="Stop Voltage (V):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.stop_voltage_var = tk.StringVar(value="1.0")
        ttk.Entry(params_frame, textvariable=self.stop_voltage_var, width=10).grid(row=1, column=1, padx=5, pady=2)
        
        # Number of points
        ttk.Label(params_frame, text="Number of Points:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.num_points_var = tk.StringVar(value="21")
        ttk.Entry(params_frame, textvariable=self.num_points_var, width=10).grid(row=2, column=1, padx=5, pady=2)
        
        # Current limit
        ttk.Label(params_frame, text="Current Limit (A):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.current_limit_var = tk.StringVar(value="0.01")
        ttk.Entry(params_frame, textvariable=self.current_limit_var, width=10).grid(row=3, column=1, padx=5, pady=2)
        
        # Control buttons
        control_frame = ttk.LabelFrame(self.control_frame, text="Experiment Control")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="Start Experiment", command=self._start_experiment)
        self.start_button.pack(fill=tk.X, padx=5, pady=2)
        
        self.pause_button = ttk.Button(control_frame, text="Pause", command=self._pause_experiment, state=tk.DISABLED)
        self.pause_button.pack(fill=tk.X, padx=5, pady=2)
        
        self.resume_button = ttk.Button(control_frame, text="Resume", command=self._resume_experiment, state=tk.DISABLED)
        self.resume_button.pack(fill=tk.X, padx=5, pady=2)
        
        self.stop_button = ttk.Button(control_frame, text="Stop", command=self._stop_experiment, state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, padx=5, pady=2)
        
        # Status display
        status_frame = ttk.LabelFrame(self.control_frame, text="Status")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Progress bar
        ttk.Label(status_frame, text="Progress:").pack(anchor=tk.W, padx=5, pady=2)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=2)
        
        # Status text
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(status_frame, textvariable=self.status_var).pack(anchor=tk.W, padx=5, pady=2)
        
        # Data count
        self.data_count_var = tk.StringVar(value="Data points: 0")
        ttk.Label(status_frame, textvariable=self.data_count_var).pack(anchor=tk.W, padx=5, pady=2)
    
    def _setup_plotting_area(self):
        """Setup the plotting area."""
        # Create notebook for different plots
        self.plot_notebook = ttk.Notebook(self.plot_frame)
        self.plot_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # I-V plot tab
        self.iv_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(self.iv_frame, text="I-V Curve")
        
        self.iv_plot = RealTimePlotWidget(
            parent=self.iv_frame,
            max_points=1000,
            update_interval=100
        )
        self.iv_plot.set_labels("Voltage (V)", "Current (A)", "I-V Curve")
        self.iv_plot.set_line_style(color='blue', width=2.0)
        
        # Multi-channel plot tab
        self.multi_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(self.multi_frame, text="Multi-channel")
        
        self.multi_plot = MultiChannelPlotWidget(
            parent=self.multi_frame,
            num_channels=3,
            max_points=1000,
            update_interval=100
        )
        self.multi_plot.set_channel_label(0, "Voltage (V)")
        self.multi_plot.set_channel_label(1, "Current (A)")
        self.multi_plot.set_channel_label(2, "Power (W)")
        
        # Start animations
        self.iv_plot.start_animation()
        self.multi_plot.start_animation()
    
    def _setup_callbacks(self):
        """Setup experiment runner callbacks."""
        self.experiment_runner.add_progress_callback(self._on_progress)
        self.experiment_runner.add_data_callback(self._on_data)
        self.experiment_runner.add_state_callback(self._on_state)
        self.experiment_runner.add_error_callback(self._on_error)
    
    def _start_experiment(self):
        """Start the I-V curve experiment."""
        try:
            # Get parameters
            start_voltage = float(self.start_voltage_var.get())
            stop_voltage = float(self.stop_voltage_var.get())
            num_points = int(self.num_points_var.get())
            current_limit = float(self.current_limit_var.get())
            
            # Clear plots
            self.iv_plot.clear_data()
            self.multi_plot.clear_data()
            
            # Create procedure
            procedure = IVCurveProcedure()
            
            # Start experiment
            success = self.experiment_runner.start_procedure(
                procedure,
                smu=self.smu,
                start_voltage=start_voltage,
                stop_voltage=stop_voltage,
                num_points=num_points,
                current_limit=current_limit,
                settling_time=0.1,
                measurement_delay=0.05
            )
            
            if success:
                self.status_var.set("Experiment started")
                self._update_button_states()
            else:
                self.status_var.set("Failed to start experiment")
                
        except Exception as e:
            self.status_var.set(f"Error: {e}")
            logger.error(f"Error starting experiment: {e}")
    
    def _pause_experiment(self):
        """Pause the experiment."""
        if self.experiment_runner.pause_procedure():
            self.status_var.set("Experiment paused")
            self._update_button_states()
    
    def _resume_experiment(self):
        """Resume the experiment."""
        if self.experiment_runner.resume_procedure():
            self.status_var.set("Experiment resumed")
            self._update_button_states()
    
    def _stop_experiment(self):
        """Stop the experiment."""
        if self.experiment_runner.stop_procedure():
            self.status_var.set("Stopping experiment...")
            self._update_button_states()
    
    def _on_progress(self, progress):
        """Handle progress updates."""
        self.progress_var.set(progress)
    
    def _on_data(self, data_point):
        """Handle data updates."""
        # Update plots if this is a measurement point
        if 'voltage' in data_point.measurements and 'current' in data_point.measurements:
            voltage = data_point.measurements['voltage']
            current = data_point.measurements['current']
            power = data_point.measurements.get('power', voltage * current)
            
            # Add to I-V plot
            self.iv_plot.add_point(voltage, current)
            
            # Add to multi-channel plot
            timestamp = data_point.timestamp
            self.multi_plot.add_point(0, timestamp, voltage)
            self.multi_plot.add_point(1, timestamp, current)
            self.multi_plot.add_point(2, timestamp, power)
        
        # Update data count
        data_count = len(self.experiment_runner.get_collected_data())
        self.data_count_var.set(f"Data points: {data_count}")
    
    def _on_state(self, state):
        """Handle state changes."""
        self.status_var.set(f"State: {state.value}")
        self._update_button_states()
    
    def _on_error(self, error_message):
        """Handle errors."""
        self.status_var.set(f"Error: {error_message}")
        self._update_button_states()
    
    def _update_button_states(self):
        """Update button states based on experiment state."""
        state = self.experiment_runner.state
        
        if state == ExperimentState.IDLE:
            self.start_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.DISABLED)
            self.resume_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.DISABLED)
        elif state == ExperimentState.RUNNING:
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)
            self.resume_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
        elif state == ExperimentState.PAUSED:
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.DISABLED)
            self.resume_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)
        else:
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.DISABLED)
            self.resume_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.DISABLED)
    
    def _schedule_update(self):
        """Schedule periodic updates."""
        # Process experiment updates
        updates = self.experiment_runner.process_updates()
        for update in updates:
            # Updates are already handled by callbacks
            pass
        
        # Schedule next update
        self.root.after(100, self._schedule_update)
    
    def run(self):
        """Run the GUI application."""
        try:
            self.root.mainloop()
        finally:
            # Cleanup
            self.experiment_runner.stop_procedure()
            self.smu.close()


def test_command_line():
    """Test the experiment runner from command line."""
    print("\n" + "="*50)
    print("Multi-threaded Experiment Runner Test")
    print("="*50)
    
    # Create experiment runner and mock SMU
    runner = ExperimentRunner()
    smu = create_mock_smu()
    
    # Setup callbacks
    def progress_callback(progress):
        print(f"Progress: {progress:.1f}%")
    
    def data_callback(data_point):
        if 'voltage' in data_point.measurements:
            v = data_point.measurements['voltage']
            i = data_point.measurements['current']
            print(f"  Data: V={v:.3f}V, I={i:.6f}A")
    
    def state_callback(state):
        print(f"State: {state.value}")
    
    def error_callback(error):
        print(f"Error: {error}")
    
    runner.add_progress_callback(progress_callback)
    runner.add_data_callback(data_callback)
    runner.add_state_callback(state_callback)
    runner.add_error_callback(error_callback)
    
    # Create and start procedure
    procedure = IVCurveProcedure()
    
    print("Starting I-V curve experiment...")
    success = runner.start_procedure(
        procedure,
        smu=smu,
        start_voltage=-0.5,
        stop_voltage=0.5,
        num_points=11,
        current_limit=0.01,
        settling_time=0.05
    )
    
    if success:
        # Wait for completion
        result = runner.wait_for_completion(timeout=30)
        
        print(f"\nExperiment completed!")
        print(f"Success: {result.success if result else False}")
        print(f"Data points collected: {len(runner.get_collected_data())}")
        
        if result and result.success:
            analysis = procedure.analyze_results()
            print(f"Average resistance: {analysis.get('avg_resistance', 'N/A'):.1f}Ω" if 'avg_resistance' in analysis else "Average resistance: N/A")
    else:
        print("Failed to start experiment")
    
    # Cleanup
    smu.close()


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "cli":
        test_command_line()
    else:
        # Run GUI demo
        print("Starting multi-threaded experiment runner GUI...")
        print("Close the window to exit.")
        app = ExperimentRunnerGUI()
        app.run()
