"""
Multi-threaded experiment runner for laboratory automation framework.

This module provides a thread-safe experiment execution system that can
run procedures in the background while updating GUI components in real-time.
"""

import time
import logging
import threading
import queue
from typing import Dict, Any, List, Callable, Optional
from dataclasses import dataclass
from enum import Enum

from procedures.base import BaseProcedure, DataPoint, ProcedureState, ProcedureResult
from procedures.iv_curve import IVCurveProcedure

logger = logging.getLogger(__name__)


class ExperimentState(Enum):
    """Enumeration of experiment runner states."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"


@dataclass
class ExperimentUpdate:
    """Update message from experiment to GUI."""
    update_type: str  # 'progress', 'data', 'state', 'error'
    data: Any
    timestamp: float


class ExperimentRunner:
    """
    Multi-threaded experiment runner.
    
    This class manages the execution of experimental procedures in separate
    threads while providing thread-safe communication with GUI components.
    """
    
    def __init__(self):
        """Initialize the experiment runner."""
        self.state = ExperimentState.IDLE
        self.current_procedure: Optional[BaseProcedure] = None
        self.current_result: Optional[ProcedureResult] = None
        
        # Threading
        self.experiment_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        
        # Communication queues
        self.update_queue = queue.Queue()
        self.command_queue = queue.Queue()
        
        # Callbacks for GUI updates
        self.progress_callbacks: List[Callable[[float], None]] = []
        self.data_callbacks: List[Callable[[DataPoint], None]] = []
        self.state_callbacks: List[Callable[[ExperimentState], None]] = []
        self.error_callbacks: List[Callable[[str], None]] = []
        
        # Data collection
        self.collected_data: List[DataPoint] = []
        
        logger.info("Experiment runner initialized")
    
    def start_procedure(self, procedure: BaseProcedure, **kwargs) -> bool:
        """
        Start a procedure in a separate thread.
        
        Args:
            procedure: The procedure to execute
            **kwargs: Parameters for the procedure
            
        Returns:
            True if started successfully, False otherwise
        """
        if self.state != ExperimentState.IDLE:
            logger.warning(f"Cannot start procedure in state: {self.state}")
            return False
        
        try:
            self.current_procedure = procedure
            self.collected_data.clear()
            self.stop_event.clear()
            self.pause_event.clear()
            
            # Setup procedure callbacks
            procedure.add_progress_callback(self._on_procedure_progress)
            procedure.add_data_callback(self._on_procedure_data)
            procedure.add_state_callback(self._on_procedure_state)
            
            # Start the experiment thread
            self.experiment_thread = threading.Thread(
                target=self._run_experiment,
                args=(procedure, kwargs),
                daemon=True
            )
            self.experiment_thread.start()
            
            self._set_state(ExperimentState.RUNNING)
            logger.info(f"Started procedure: {procedure.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting procedure: {e}")
            self._notify_error(str(e))
            return False
    
    def pause_procedure(self) -> bool:
        """
        Pause the current procedure.
        
        Returns:
            True if paused successfully, False otherwise
        """
        if self.state == ExperimentState.RUNNING and self.current_procedure:
            try:
                self.current_procedure.pause()
                self._set_state(ExperimentState.PAUSED)
                logger.info("Procedure paused")
                return True
            except Exception as e:
                logger.error(f"Error pausing procedure: {e}")
                return False
        return False
    
    def resume_procedure(self) -> bool:
        """
        Resume the current procedure.
        
        Returns:
            True if resumed successfully, False otherwise
        """
        if self.state == ExperimentState.PAUSED and self.current_procedure:
            try:
                self.current_procedure.resume()
                self._set_state(ExperimentState.RUNNING)
                logger.info("Procedure resumed")
                return True
            except Exception as e:
                logger.error(f"Error resuming procedure: {e}")
                return False
        return False
    
    def stop_procedure(self) -> bool:
        """
        Stop the current procedure.
        
        Returns:
            True if stop initiated successfully, False otherwise
        """
        if self.state in [ExperimentState.RUNNING, ExperimentState.PAUSED]:
            try:
                self._set_state(ExperimentState.STOPPING)
                self.stop_event.set()
                
                if self.current_procedure:
                    self.current_procedure.stop()
                
                logger.info("Procedure stop initiated")
                return True
            except Exception as e:
                logger.error(f"Error stopping procedure: {e}")
                return False
        return False
    
    def wait_for_completion(self, timeout: Optional[float] = None) -> Optional[ProcedureResult]:
        """
        Wait for the current procedure to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            ProcedureResult if completed, None if timeout
        """
        if self.experiment_thread:
            self.experiment_thread.join(timeout)
        
        return self.current_result
    
    def get_collected_data(self) -> List[DataPoint]:
        """Get all collected data points."""
        return self.collected_data.copy()
    
    def get_latest_data(self, count: int = 10) -> List[DataPoint]:
        """
        Get the latest data points.
        
        Args:
            count: Number of latest points to return
            
        Returns:
            List of latest data points
        """
        return self.collected_data[-count:] if len(self.collected_data) >= count else self.collected_data.copy()
    
    def _run_experiment(self, procedure: BaseProcedure, kwargs: Dict[str, Any]) -> None:
        """
        Run the experiment in a separate thread.
        
        Args:
            procedure: The procedure to execute
            kwargs: Parameters for the procedure
        """
        try:
            # Start the procedure
            procedure.start(**kwargs)
            
            # Wait for completion or stop signal
            while procedure.state in [ProcedureState.RUNNING, ProcedureState.PAUSED]:
                if self.stop_event.is_set():
                    break
                time.sleep(0.1)
            
            # Get the result
            self.current_result = procedure.wait_for_completion(timeout=1.0)
            
            # Update state based on result
            if self.current_result:
                if self.current_result.state == ProcedureState.COMPLETED:
                    self._set_state(ExperimentState.STOPPED)
                elif self.current_result.state == ProcedureState.CANCELLED:
                    self._set_state(ExperimentState.STOPPED)
                elif self.current_result.state == ProcedureState.FAILED:
                    self._set_state(ExperimentState.STOPPED)
                    self._notify_error(self.current_result.error_message or "Procedure failed")
            else:
                self._set_state(ExperimentState.STOPPED)
                self._notify_error("Procedure completed without result")
            
            logger.info(f"Experiment completed: {procedure.name}")
            
        except Exception as e:
            logger.error(f"Error in experiment thread: {e}")
            self._set_state(ExperimentState.STOPPED)
            self._notify_error(str(e))
        
        finally:
            # Reset to idle state after a brief delay
            time.sleep(0.5)
            self._set_state(ExperimentState.IDLE)
    
    def _on_procedure_progress(self, progress: float) -> None:
        """Handle procedure progress updates."""
        try:
            update = ExperimentUpdate(
                update_type='progress',
                data=progress,
                timestamp=time.time()
            )
            self.update_queue.put_nowait(update)
            
            # Notify callbacks
            for callback in self.progress_callbacks:
                try:
                    callback(progress)
                except Exception as e:
                    logger.error(f"Error in progress callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling progress update: {e}")
    
    def _on_procedure_data(self, data_point: DataPoint) -> None:
        """Handle procedure data updates."""
        try:
            # Store data
            self.collected_data.append(data_point)
            
            # Send update
            update = ExperimentUpdate(
                update_type='data',
                data=data_point,
                timestamp=time.time()
            )
            self.update_queue.put_nowait(update)
            
            # Notify callbacks
            for callback in self.data_callbacks:
                try:
                    callback(data_point)
                except Exception as e:
                    logger.error(f"Error in data callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling data update: {e}")
    
    def _on_procedure_state(self, procedure_state: ProcedureState) -> None:
        """Handle procedure state changes."""
        try:
            update = ExperimentUpdate(
                update_type='state',
                data=procedure_state,
                timestamp=time.time()
            )
            self.update_queue.put_nowait(update)
            
        except Exception as e:
            logger.error(f"Error handling state update: {e}")
    
    def _set_state(self, new_state: ExperimentState) -> None:
        """Set the experiment runner state."""
        if self.state != new_state:
            old_state = self.state
            self.state = new_state
            
            logger.debug(f"Experiment runner state: {old_state.value} -> {new_state.value}")
            
            # Notify callbacks
            for callback in self.state_callbacks:
                try:
                    callback(new_state)
                except Exception as e:
                    logger.error(f"Error in state callback: {e}")
    
    def _notify_error(self, error_message: str) -> None:
        """Notify error callbacks."""
        try:
            update = ExperimentUpdate(
                update_type='error',
                data=error_message,
                timestamp=time.time()
            )
            self.update_queue.put_nowait(update)
            
            # Notify callbacks
            for callback in self.error_callbacks:
                try:
                    callback(error_message)
                except Exception as e:
                    logger.error(f"Error in error callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error notifying error: {e}")
    
    def add_progress_callback(self, callback: Callable[[float], None]) -> None:
        """Add a progress callback."""
        self.progress_callbacks.append(callback)
    
    def add_data_callback(self, callback: Callable[[DataPoint], None]) -> None:
        """Add a data callback."""
        self.data_callbacks.append(callback)
    
    def add_state_callback(self, callback: Callable[[ExperimentState], None]) -> None:
        """Add a state callback."""
        self.state_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str], None]) -> None:
        """Add an error callback."""
        self.error_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[float], None]) -> None:
        """Remove a progress callback."""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)
    
    def remove_data_callback(self, callback: Callable[[DataPoint], None]) -> None:
        """Remove a data callback."""
        if callback in self.data_callbacks:
            self.data_callbacks.remove(callback)
    
    def remove_state_callback(self, callback: Callable[[ExperimentState], None]) -> None:
        """Remove a state callback."""
        if callback in self.state_callbacks:
            self.state_callbacks.remove(callback)
    
    def remove_error_callback(self, callback: Callable[[str], None]) -> None:
        """Remove an error callback."""
        if callback in self.error_callbacks:
            self.error_callbacks.remove(callback)
    
    def process_updates(self) -> List[ExperimentUpdate]:
        """
        Process all pending updates from the experiment thread.
        
        This method should be called periodically from the main thread
        to handle updates from the experiment thread.
        
        Returns:
            List of updates to process
        """
        updates = []
        try:
            while True:
                update = self.update_queue.get_nowait()
                updates.append(update)
        except queue.Empty:
            pass
        
        return updates
