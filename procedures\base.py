"""
Base classes for experimental procedures.

This module provides the foundation for creating automated experimental
procedures with data collection, progress tracking, and error handling.
"""

import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Callable, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import threading
import queue

logger = logging.getLogger(__name__)


class ProcedureState(Enum):
    """Enumeration of procedure execution states."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class DataPoint:
    """Data point collected during a procedure."""
    timestamp: float
    step: int
    parameters: Dict[str, Any] = field(default_factory=dict)
    measurements: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ProcedureResult:
    """Result of a procedure execution."""
    success: bool
    state: ProcedureState
    data_points: List[DataPoint] = field(default_factory=list)
    error_message: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> Optional[float]:
        """Get the procedure duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class BaseProcedure(ABC):
    """
    Base class for all experimental procedures.
    
    This class provides the framework for creating automated experimental
    procedures with standardized execution, data collection, and error handling.
    """
    
    def __init__(self, name: str, description: str = ""):
        """
        Initialize the procedure.
        
        Args:
            name: Name of the procedure
            description: Description of the procedure
        """
        self.name = name
        self.description = description
        
        # Execution state
        self.state = ProcedureState.IDLE
        self.progress = 0.0  # Progress as percentage (0-100)
        
        # Data collection
        self.data_points: List[DataPoint] = []
        self.current_step = 0
        
        # Threading and control
        self._thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._pause_event = threading.Event()
        
        # Callbacks
        self.progress_callbacks: List[Callable[[float], None]] = []
        self.data_callbacks: List[Callable[[DataPoint], None]] = []
        self.state_callbacks: List[Callable[[ProcedureState], None]] = []
        
        # Result
        self.result: Optional[ProcedureResult] = None
        
        logger.info(f"Initialized procedure: {self.name}")
    
    @abstractmethod
    def setup(self, **kwargs) -> None:
        """
        Setup the procedure with parameters.
        
        This method should be implemented by subclasses to configure
        the procedure with specific parameters.
        
        Args:
            **kwargs: Procedure-specific parameters
        """
        pass
    
    @abstractmethod
    def execute_step(self, step: int) -> DataPoint:
        """
        Execute a single step of the procedure.
        
        This method should be implemented by subclasses to define
        the actions for each step of the procedure.
        
        Args:
            step: Step number (0-based)
            
        Returns:
            DataPoint collected during this step
        """
        pass
    
    @abstractmethod
    def get_total_steps(self) -> int:
        """
        Get the total number of steps in the procedure.
        
        Returns:
            Total number of steps
        """
        pass
    
    def cleanup(self) -> None:
        """
        Cleanup after procedure execution.
        
        This method can be overridden by subclasses to perform
        cleanup operations after the procedure completes.
        """
        pass
    
    def start(self, **kwargs) -> None:
        """
        Start the procedure execution.
        
        Args:
            **kwargs: Procedure-specific parameters
        """
        if self.state != ProcedureState.IDLE:
            raise RuntimeError(f"Cannot start procedure in state: {self.state}")
        
        # Setup the procedure
        self.setup(**kwargs)
        
        # Reset state
        self.data_points.clear()
        self.current_step = 0
        self.progress = 0.0
        self._stop_event.clear()
        self._pause_event.clear()
        
        # Start execution thread
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()
        
        logger.info(f"Started procedure: {self.name}")
    
    def stop(self) -> None:
        """Stop the procedure execution."""
        if self.state in [ProcedureState.RUNNING, ProcedureState.PAUSED]:
            self._stop_event.set()
            logger.info(f"Stopping procedure: {self.name}")
    
    def pause(self) -> None:
        """Pause the procedure execution."""
        if self.state == ProcedureState.RUNNING:
            self._pause_event.set()
            self._set_state(ProcedureState.PAUSED)
            logger.info(f"Paused procedure: {self.name}")
    
    def resume(self) -> None:
        """Resume the procedure execution."""
        if self.state == ProcedureState.PAUSED:
            self._pause_event.clear()
            self._set_state(ProcedureState.RUNNING)
            logger.info(f"Resumed procedure: {self.name}")
    
    def wait_for_completion(self, timeout: Optional[float] = None) -> ProcedureResult:
        """
        Wait for the procedure to complete.
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            ProcedureResult object
        """
        if self._thread:
            self._thread.join(timeout)
        
        return self.result or ProcedureResult(
            success=False,
            state=self.state,
            error_message="Procedure did not complete"
        )
    
    def _run(self) -> None:
        """Internal method to run the procedure."""
        start_time = time.time()
        self._set_state(ProcedureState.RUNNING)
        
        try:
            total_steps = self.get_total_steps()
            
            for step in range(total_steps):
                # Check for stop signal
                if self._stop_event.is_set():
                    self._set_state(ProcedureState.CANCELLED)
                    break
                
                # Check for pause signal
                while self._pause_event.is_set():
                    time.sleep(0.1)
                    if self._stop_event.is_set():
                        self._set_state(ProcedureState.CANCELLED)
                        break
                
                if self.state == ProcedureState.CANCELLED:
                    break
                
                # Execute the step
                try:
                    self.current_step = step
                    data_point = self.execute_step(step)
                    self.data_points.append(data_point)
                    
                    # Update progress
                    self.progress = (step + 1) / total_steps * 100
                    self._notify_progress(self.progress)
                    self._notify_data(data_point)
                    
                except Exception as e:
                    logger.error(f"Error in step {step}: {e}")
                    self._set_state(ProcedureState.FAILED)
                    self.result = ProcedureResult(
                        success=False,
                        state=ProcedureState.FAILED,
                        data_points=self.data_points,
                        error_message=str(e),
                        start_time=start_time,
                        end_time=time.time()
                    )
                    return
            
            # Procedure completed successfully
            if self.state != ProcedureState.CANCELLED:
                self._set_state(ProcedureState.COMPLETED)
                self.result = ProcedureResult(
                    success=True,
                    state=ProcedureState.COMPLETED,
                    data_points=self.data_points,
                    start_time=start_time,
                    end_time=time.time()
                )
            else:
                self.result = ProcedureResult(
                    success=False,
                    state=ProcedureState.CANCELLED,
                    data_points=self.data_points,
                    error_message="Procedure was cancelled",
                    start_time=start_time,
                    end_time=time.time()
                )
        
        except Exception as e:
            logger.error(f"Unexpected error in procedure: {e}")
            self._set_state(ProcedureState.FAILED)
            self.result = ProcedureResult(
                success=False,
                state=ProcedureState.FAILED,
                data_points=self.data_points,
                error_message=str(e),
                start_time=start_time,
                end_time=time.time()
            )
        
        finally:
            # Cleanup
            try:
                self.cleanup()
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
    
    def _set_state(self, new_state: ProcedureState) -> None:
        """Set the procedure state and notify callbacks."""
        if self.state != new_state:
            self.state = new_state
            self._notify_state(new_state)
            logger.debug(f"Procedure {self.name} state changed to: {new_state.value}")
    
    def _notify_progress(self, progress: float) -> None:
        """Notify progress callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(progress)
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
    
    def _notify_data(self, data_point: DataPoint) -> None:
        """Notify data callbacks."""
        for callback in self.data_callbacks:
            try:
                callback(data_point)
            except Exception as e:
                logger.error(f"Error in data callback: {e}")
    
    def _notify_state(self, state: ProcedureState) -> None:
        """Notify state callbacks."""
        for callback in self.state_callbacks:
            try:
                callback(state)
            except Exception as e:
                logger.error(f"Error in state callback: {e}")
    
    def add_progress_callback(self, callback: Callable[[float], None]) -> None:
        """Add a progress callback."""
        self.progress_callbacks.append(callback)
    
    def add_data_callback(self, callback: Callable[[DataPoint], None]) -> None:
        """Add a data callback."""
        self.data_callbacks.append(callback)
    
    def add_state_callback(self, callback: Callable[[ProcedureState], None]) -> None:
        """Add a state callback."""
        self.state_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[float], None]) -> None:
        """Remove a progress callback."""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)
    
    def remove_data_callback(self, callback: Callable[[DataPoint], None]) -> None:
        """Remove a data callback."""
        if callback in self.data_callbacks:
            self.data_callbacks.remove(callback)
    
    def remove_state_callback(self, callback: Callable[[ProcedureState], None]) -> None:
        """Remove a state callback."""
        if callback in self.state_callbacks:
            self.state_callbacks.remove(callback)
