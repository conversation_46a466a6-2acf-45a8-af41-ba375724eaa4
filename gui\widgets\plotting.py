"""
Real-time plotting widgets for laboratory automation framework.

This module provides high-performance plotting widgets for real-time data
visualization using matplotlib as the backend.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk
import threading
import time
from collections import deque
from typing import List, Tuple, Optional, Callable
import logging

logger = logging.getLogger(__name__)


class RealTimePlotWidget:
    """
    High-performance real-time plotting widget.
    
    This widget provides real-time scrolling plots with configurable
    buffer sizes and update rates for laboratory data visualization.
    """
    
    def __init__(self, parent=None, max_points: int = 1000, update_interval: int = 50):
        """
        Initialize the real-time plot widget.
        
        Args:
            parent: Parent widget (tkinter widget)
            max_points: Maximum number of points to display
            update_interval: Update interval in milliseconds
        """
        self.parent = parent
        self.max_points = max_points
        self.update_interval = update_interval
        
        # Data storage
        self.x_data = deque(maxlen=max_points)
        self.y_data = deque(maxlen=max_points)
        self.data_lock = threading.Lock()
        
        # Plot configuration
        self.xlabel = "Time (s)"
        self.ylabel = "Value"
        self.title = "Real-time Data"
        self.line_color = 'blue'
        self.line_width = 1.0
        
        # Animation control
        self.animation = None
        self.is_running = False
        
        # Setup the plot
        self._setup_plot()
        
    def _setup_plot(self):
        """Setup the matplotlib figure and canvas."""
        # Create figure and axis
        self.figure, self.ax = plt.subplots(figsize=(8, 6))
        self.ax.set_xlabel(self.xlabel)
        self.ax.set_ylabel(self.ylabel)
        self.ax.set_title(self.title)
        self.ax.grid(True, alpha=0.3)
        
        # Create empty line
        self.line, = self.ax.plot([], [], color=self.line_color, linewidth=self.line_width)
        
        # Setup canvas if parent is provided
        if self.parent:
            self.canvas = FigureCanvasTkAgg(self.figure, self.parent)
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        logger.info("Real-time plot widget initialized")
    
    def add_point(self, x: float, y: float):
        """
        Add a new data point to the plot.
        
        Args:
            x: X-coordinate (typically time)
            y: Y-coordinate (measurement value)
        """
        with self.data_lock:
            self.x_data.append(x)
            self.y_data.append(y)
    
    def add_points(self, x_points: List[float], y_points: List[float]):
        """
        Add multiple data points to the plot.
        
        Args:
            x_points: List of X-coordinates
            y_points: List of Y-coordinates
        """
        if len(x_points) != len(y_points):
            raise ValueError("x_points and y_points must have the same length")
        
        with self.data_lock:
            for x, y in zip(x_points, y_points):
                self.x_data.append(x)
                self.y_data.append(y)
    
    def clear_data(self):
        """Clear all data points."""
        with self.data_lock:
            self.x_data.clear()
            self.y_data.clear()
    
    def _update_plot(self, frame):
        """Update the plot with new data (called by animation)."""
        with self.data_lock:
            if len(self.x_data) > 0:
                # Update line data
                self.line.set_data(list(self.x_data), list(self.y_data))
                
                # Auto-scale axes
                self.ax.relim()
                self.ax.autoscale_view()
        
        return self.line,
    
    def start_animation(self):
        """Start the real-time animation."""
        if not self.is_running:
            self.animation = animation.FuncAnimation(
                self.figure, 
                self._update_plot,
                interval=self.update_interval,
                blit=True,
                cache_frame_data=False
            )
            self.is_running = True
            logger.info("Real-time plot animation started")
    
    def stop_animation(self):
        """Stop the real-time animation."""
        if self.is_running and self.animation:
            self.animation.event_source.stop()
            self.is_running = False
            logger.info("Real-time plot animation stopped")
    
    def set_labels(self, xlabel: str, ylabel: str, title: str = None):
        """
        Set plot labels.
        
        Args:
            xlabel: X-axis label
            ylabel: Y-axis label
            title: Plot title (optional)
        """
        self.xlabel = xlabel
        self.ylabel = ylabel
        if title:
            self.title = title
        
        self.ax.set_xlabel(xlabel)
        self.ax.set_ylabel(ylabel)
        self.ax.set_title(self.title)
    
    def set_line_style(self, color: str = 'blue', width: float = 1.0):
        """
        Set line style.
        
        Args:
            color: Line color
            width: Line width
        """
        self.line_color = color
        self.line_width = width
        self.line.set_color(color)
        self.line.set_linewidth(width)
    
    def save_plot(self, filename: str, dpi: int = 300):
        """
        Save the current plot to file.
        
        Args:
            filename: Output filename
            dpi: Resolution in dots per inch
        """
        self.figure.savefig(filename, dpi=dpi, bbox_inches='tight')
        logger.info(f"Plot saved to {filename}")


class MultiChannelPlotWidget:
    """
    Multi-channel real-time plotting widget.
    
    This widget can display multiple data channels simultaneously
    with different colors and styles.
    """
    
    def __init__(self, parent=None, num_channels: int = 2, max_points: int = 1000, 
                 update_interval: int = 50):
        """
        Initialize the multi-channel plot widget.
        
        Args:
            parent: Parent widget
            num_channels: Number of data channels
            max_points: Maximum number of points per channel
            update_interval: Update interval in milliseconds
        """
        self.parent = parent
        self.num_channels = num_channels
        self.max_points = max_points
        self.update_interval = update_interval
        
        # Data storage for each channel
        self.x_data = [deque(maxlen=max_points) for _ in range(num_channels)]
        self.y_data = [deque(maxlen=max_points) for _ in range(num_channels)]
        self.data_lock = threading.Lock()
        
        # Channel configuration
        self.channel_colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']
        self.channel_labels = [f'Channel {i+1}' for i in range(num_channels)]
        
        # Plot configuration
        self.xlabel = "Time (s)"
        self.ylabel = "Value"
        self.title = "Multi-channel Real-time Data"
        
        # Animation control
        self.animation = None
        self.is_running = False
        
        # Setup the plot
        self._setup_plot()
    
    def _setup_plot(self):
        """Setup the matplotlib figure and canvas."""
        # Create figure and axis
        self.figure, self.ax = plt.subplots(figsize=(10, 6))
        self.ax.set_xlabel(self.xlabel)
        self.ax.set_ylabel(self.ylabel)
        self.ax.set_title(self.title)
        self.ax.grid(True, alpha=0.3)
        
        # Create lines for each channel
        self.lines = []
        for i in range(self.num_channels):
            color = self.channel_colors[i % len(self.channel_colors)]
            label = self.channel_labels[i]
            line, = self.ax.plot([], [], color=color, label=label, linewidth=1.0)
            self.lines.append(line)
        
        # Add legend
        self.ax.legend()
        
        # Setup canvas if parent is provided
        if self.parent:
            self.canvas = FigureCanvasTkAgg(self.figure, self.parent)
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        logger.info(f"Multi-channel plot widget initialized with {self.num_channels} channels")
    
    def add_point(self, channel: int, x: float, y: float):
        """
        Add a data point to a specific channel.
        
        Args:
            channel: Channel index (0-based)
            x: X-coordinate
            y: Y-coordinate
        """
        if 0 <= channel < self.num_channels:
            with self.data_lock:
                self.x_data[channel].append(x)
                self.y_data[channel].append(y)
        else:
            raise ValueError(f"Channel {channel} out of range (0-{self.num_channels-1})")
    
    def add_points(self, channel: int, x_points: List[float], y_points: List[float]):
        """
        Add multiple data points to a specific channel.
        
        Args:
            channel: Channel index
            x_points: List of X-coordinates
            y_points: List of Y-coordinates
        """
        if len(x_points) != len(y_points):
            raise ValueError("x_points and y_points must have the same length")
        
        if 0 <= channel < self.num_channels:
            with self.data_lock:
                for x, y in zip(x_points, y_points):
                    self.x_data[channel].append(x)
                    self.y_data[channel].append(y)
        else:
            raise ValueError(f"Channel {channel} out of range (0-{self.num_channels-1})")
    
    def clear_data(self, channel: Optional[int] = None):
        """
        Clear data for a specific channel or all channels.
        
        Args:
            channel: Channel index (None for all channels)
        """
        with self.data_lock:
            if channel is None:
                # Clear all channels
                for i in range(self.num_channels):
                    self.x_data[i].clear()
                    self.y_data[i].clear()
            elif 0 <= channel < self.num_channels:
                self.x_data[channel].clear()
                self.y_data[channel].clear()
            else:
                raise ValueError(f"Channel {channel} out of range (0-{self.num_channels-1})")
    
    def _update_plot(self, frame):
        """Update the plot with new data (called by animation)."""
        with self.data_lock:
            for i in range(self.num_channels):
                if len(self.x_data[i]) > 0:
                    self.lines[i].set_data(list(self.x_data[i]), list(self.y_data[i]))
            
            # Auto-scale axes
            self.ax.relim()
            self.ax.autoscale_view()
        
        return self.lines
    
    def start_animation(self):
        """Start the real-time animation."""
        if not self.is_running:
            self.animation = animation.FuncAnimation(
                self.figure,
                self._update_plot,
                interval=self.update_interval,
                blit=True,
                cache_frame_data=False
            )
            self.is_running = True
            logger.info("Multi-channel plot animation started")
    
    def stop_animation(self):
        """Stop the real-time animation."""
        if self.is_running and self.animation:
            self.animation.event_source.stop()
            self.is_running = False
            logger.info("Multi-channel plot animation stopped")
    
    def set_channel_label(self, channel: int, label: str):
        """
        Set label for a specific channel.
        
        Args:
            channel: Channel index
            label: Channel label
        """
        if 0 <= channel < self.num_channels:
            self.channel_labels[channel] = label
            self.lines[channel].set_label(label)
            self.ax.legend()
        else:
            raise ValueError(f"Channel {channel} out of range (0-{self.num_channels-1})")
    
    def save_plot(self, filename: str, dpi: int = 300):
        """
        Save the current plot to file.
        
        Args:
            filename: Output filename
            dpi: Resolution in dots per inch
        """
        self.figure.savefig(filename, dpi=dpi, bbox_inches='tight')
        logger.info(f"Multi-channel plot saved to {filename}")
