"""
Test script for configuration manager.

This script demonstrates the configuration-driven instrument loading
capabilities of the laboratory automation framework.
"""

import sys
import os
import logging

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config_manager import ConfigurationManager, get_config_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_config_loading():
    """Test configuration loading and validation."""
    print("\n" + "="*50)
    print("Configuration Manager Test")
    print("="*50)
    
    # Create configuration manager
    config_mgr = ConfigurationManager()
    
    # Test framework config
    framework_config = config_mgr.get_framework_config()
    print(f"\nFramework: {framework_config.get('name', 'Unknown')} v{framework_config.get('version', '0.0.0')}")
    
    # Test instrument configs
    instrument_configs = config_mgr.get_all_instrument_configs()
    print(f"\nConfigured instruments ({len(instrument_configs)}):")
    for name, config in instrument_configs.items():
        enabled = "✓" if config.get("enabled", True) else "✗"
        auto_connect = "✓" if config.get("auto_connect", True) else "✗"
        print(f"  {enabled} {name}: {config.get('type', 'Unknown')} - {config.get('name', name)}")
        print(f"    Resource: {config.get('adapter', {}).get('resource', 'Unknown')}")
        print(f"    Auto-connect: {auto_connect}")
    
    # Test procedure configs
    procedure_configs = config_mgr.get_all_procedure_configs()
    print(f"\nConfigured procedures ({len(procedure_configs)}):")
    for name, config in procedure_configs.items():
        enabled = "✓" if config.get("enabled", True) else "✗"
        print(f"  {enabled} {name}: {config.get('type', 'Unknown')} - {config.get('name', name)}")
    
    # Test GUI config
    gui_config = config_mgr.get_gui_config()
    window_size = gui_config.get("window_size", {})
    print(f"\nGUI Configuration:")
    print(f"  Theme: {gui_config.get('theme', 'default')}")
    print(f"  Window size: {window_size.get('width', 800)}x{window_size.get('height', 600)}")
    
    return config_mgr


def test_instrument_loading():
    """Test loading instruments from configuration."""
    print("\n" + "="*50)
    print("Instrument Loading Test")
    print("="*50)
    
    # Get configuration manager
    config_mgr = get_config_manager()
    
    # Load instruments
    print("\nLoading instruments...")
    instruments = config_mgr.load_instruments(auto_connect=True)
    
    print(f"\nLoaded {len(instruments)} instrument(s):")
    for name, instrument in instruments.items():
        print(f"  ✓ {name}: {instrument.__class__.__name__}")
        try:
            print(f"    ID: {instrument.id}")
            print(f"    Adapter: {instrument.adapter.__class__.__name__}")
            print(f"    Resource: {instrument.adapter.resource_name}")
        except Exception as e:
            print(f"    Error getting info: {e}")
    
    # Test instrument functionality
    if instruments:
        print("\nTesting instrument functionality...")
        for name, instrument in instruments.items():
            try:
                print(f"\n  Testing {name}:")
                
                # Test basic properties
                if hasattr(instrument, 'source_voltage'):
                    voltage = instrument.source_voltage
                    print(f"    Source voltage: {voltage} V")
                
                if hasattr(instrument, 'compliance_current'):
                    current = instrument.compliance_current
                    print(f"    Compliance current: {current} A")
                
                if hasattr(instrument, 'source_enabled'):
                    enabled = instrument.source_enabled
                    print(f"    Output enabled: {enabled}")
                
                # Test a simple configuration
                if hasattr(instrument, 'configure_voltage_source'):
                    instrument.configure_voltage_source(voltage=1.0, current_limit=0.001)
                    print(f"    ✓ Configured as voltage source")
                
            except Exception as e:
                print(f"    ✗ Error testing {name}: {e}")
    
    # Clean up
    print("\nCleaning up...")
    config_mgr.close_all_instruments()
    print("All instruments closed.")
    
    return instruments


def test_config_modification():
    """Test modifying configuration programmatically."""
    print("\n" + "="*50)
    print("Configuration Modification Test")
    print("="*50)
    
    # Create a new configuration manager with a test config
    config_mgr = ConfigurationManager("test_config.json")
    
    # Add a new instrument configuration
    new_instrument_config = {
        "type": "Keithley2400",
        "adapter": {
            "type": "MockAdapter",
            "resource": "MOCK::TEST_SMU::INSTR"
        },
        "name": "Test SMU",
        "description": "Test instrument added programmatically",
        "enabled": True,
        "auto_connect": True,
        "settings": {
            "source_function": "VOLT",
            "source_voltage": 2.5,
            "compliance_current": 0.005
        }
    }
    
    config_mgr.update_instrument_config("test_smu", new_instrument_config)
    print("Added new instrument configuration: test_smu")
    
    # Save the configuration
    config_mgr.save_config()
    print("Saved configuration to test_config.json")
    
    # Load the instrument
    instruments = config_mgr.load_instruments()
    if "test_smu" in instruments:
        test_smu = instruments["test_smu"]
        print(f"✓ Successfully loaded test instrument: {test_smu.name}")
        print(f"  Source voltage: {test_smu.source_voltage} V")
        print(f"  Compliance current: {test_smu.compliance_current} A")
    
    # Clean up
    config_mgr.close_all_instruments()
    
    # Remove test config file
    if os.path.exists("test_config.json"):
        os.remove("test_config.json")
        print("Cleaned up test configuration file")


def test_error_handling():
    """Test error handling for invalid configurations."""
    print("\n" + "="*50)
    print("Error Handling Test")
    print("="*50)
    
    config_mgr = ConfigurationManager()
    
    # Test invalid instrument type
    try:
        invalid_config = {
            "type": "NonExistentInstrument",
            "adapter": {
                "type": "MockAdapter",
                "resource": "MOCK::INVALID::INSTR"
            }
        }
        config_mgr.create_instrument("invalid", invalid_config)
        print("✗ Should have failed for invalid instrument type")
    except ValueError as e:
        print(f"✓ Correctly caught invalid instrument type: {e}")
    
    # Test invalid adapter type
    try:
        invalid_config = {
            "type": "Keithley2400",
            "adapter": {
                "type": "NonExistentAdapter",
                "resource": "INVALID::RESOURCE"
            }
        }
        config_mgr.create_instrument("invalid", invalid_config)
        print("✗ Should have failed for invalid adapter type")
    except ValueError as e:
        print(f"✓ Correctly caught invalid adapter type: {e}")
    
    print("Error handling tests completed")


def interactive_menu():
    """Provide an interactive menu for testing configuration features."""
    while True:
        print("\n" + "="*50)
        print("Configuration Manager Test Menu")
        print("="*50)
        print("1. Test configuration loading")
        print("2. Test instrument loading")
        print("3. Test configuration modification")
        print("4. Test error handling")
        print("5. Run all tests")
        print("6. Exit")
        
        try:
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == "1":
                test_config_loading()
            elif choice == "2":
                test_instrument_loading()
            elif choice == "3":
                test_config_modification()
            elif choice == "4":
                test_error_handling()
            elif choice == "5":
                test_config_loading()
                test_instrument_loading()
                test_config_modification()
                test_error_handling()
            elif choice == "6":
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1, 2, 3, 4, 5, or 6.")
                
        except KeyboardInterrupt:
            print("\n\nExiting...")
            break
        except Exception as e:
            logger.error(f"Error in interactive menu: {e}")
            print(f"An error occurred: {e}")


if __name__ == "__main__":
    try:
        if len(sys.argv) > 1:
            if sys.argv[1] == "load":
                test_config_loading()
            elif sys.argv[1] == "instruments":
                test_instrument_loading()
            elif sys.argv[1] == "modify":
                test_config_modification()
            elif sys.argv[1] == "errors":
                test_error_handling()
            elif sys.argv[1] == "all":
                test_config_loading()
                test_instrument_loading()
                test_config_modification()
                test_error_handling()
            else:
                print("Usage: python test_config_manager.py [load|instruments|modify|errors|all]")
        else:
            interactive_menu()
            
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
        sys.exit(1)
