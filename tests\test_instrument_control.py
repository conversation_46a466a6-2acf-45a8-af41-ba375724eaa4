"""
Test script for instrument control widgets.

This script demonstrates the dynamic instrument control capabilities
of the laboratory automation framework.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from instruments.base import MockAdapter
from instruments.keithley2400 import Keithley2400
from gui.widgets.instrument_control import InstrumentWidget, InstrumentPanel


class InstrumentControlDemo:
    """Demonstration application for instrument control widgets."""
    
    def __init__(self):
        """Initialize the demo application."""
        self.root = tk.Tk()
        self.root.title("Laboratory Automation Framework - Instrument Control Demo")
        self.root.geometry("1000x700")
        
        # Create instruments for testing
        self._create_test_instruments()
        
        # Setup the GUI
        self._setup_gui()
    
    def _create_test_instruments(self):
        """Create test instruments with mock adapters."""
        # Create mock Keithley 2400
        mock_adapter1 = MockAdapter("MOCK::KEITHLEY2400_1::INSTR")
        mock_adapter1.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39")
        mock_adapter1.add_response(":SOUR:VOLT:LEV?", "0.000000")
        mock_adapter1.add_response(":SENS:CURR:PROT?", "1.050000E-03")
        mock_adapter1.add_response(":OUTP?", "0")
        mock_adapter1.add_response(":SOUR:FUNC?", "VOLT")
        
        self.smu1 = Keithley2400(mock_adapter1, name="SMU 1")
        
        # Create another mock Keithley 2400
        mock_adapter2 = MockAdapter("MOCK::KEITHLEY2400_2::INSTR")
        mock_adapter2.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,7654321,C30 Mar 17 1999 09:32:39")
        mock_adapter2.add_response(":SOUR:VOLT:LEV?", "1.500000")
        mock_adapter2.add_response(":SENS:CURR:PROT?", "5.000000E-03")
        mock_adapter2.add_response(":OUTP?", "1")
        mock_adapter2.add_response(":SOUR:FUNC?", "CURR")
        
        self.smu2 = Keithley2400(mock_adapter2, name="SMU 2")
    
    def _setup_gui(self):
        """Setup the GUI layout."""
        # Create notebook for different demo modes
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Single instrument demo
        self.single_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.single_frame, text="Single Instrument")
        
        # Multi-instrument panel demo
        self.panel_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.panel_frame, text="Instrument Panel")
        
        # Setup single instrument demo
        self._setup_single_instrument_demo()
        
        # Setup instrument panel demo
        self._setup_instrument_panel_demo()
        
        # Control panel
        self._setup_controls()
    
    def _setup_single_instrument_demo(self):
        """Setup single instrument control demo."""
        # Create instrument widget for SMU 1
        self.single_widget = InstrumentWidget(
            self.single_frame,
            self.smu1,
            "Keithley 2400 SMU #1"
        )
    
    def _setup_instrument_panel_demo(self):
        """Setup instrument panel demo."""
        # Create instrument panel
        self.instrument_panel = InstrumentPanel(self.panel_frame)
        
        # Add instruments to panel
        self.instrument_panel.add_instrument("SMU 1", self.smu1)
        self.instrument_panel.add_instrument("SMU 2", self.smu2)
    
    def _setup_controls(self):
        """Setup control panel."""
        # Control frame
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Test configuration button
        test_config_button = ttk.Button(
            control_frame,
            text="Apply Test Configuration",
            command=self.apply_test_configuration
        )
        test_config_button.pack(side=tk.LEFT, padx=5)
        
        # Reset button
        reset_button = ttk.Button(
            control_frame,
            text="Reset Instruments",
            command=self.reset_instruments
        )
        reset_button.pack(side=tk.LEFT, padx=5)
        
        # Refresh button
        refresh_button = ttk.Button(
            control_frame,
            text="Refresh All",
            command=self.refresh_all
        )
        refresh_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(control_frame, textvariable=self.status_var)
        status_label.pack(side=tk.RIGHT, padx=5)
    
    def apply_test_configuration(self):
        """Apply a test configuration to the instruments."""
        try:
            # Configure SMU 1 as voltage source
            self.smu1.configure_voltage_source(voltage=2.5, current_limit=0.01)
            self.smu1.source_enabled = True
            
            # Configure SMU 2 as current source
            self.smu2.configure_current_source(current=0.005, voltage_limit=10.0)
            self.smu2.source_enabled = True
            
            # Refresh displays
            self.refresh_all()
            
            self.status_var.set("Test configuration applied")
            
        except Exception as e:
            self.status_var.set(f"Error: {e}")
    
    def reset_instruments(self):
        """Reset all instruments to default state."""
        try:
            # Reset instruments
            self.smu1.reset()
            self.smu1.source_enabled = False
            
            self.smu2.reset()
            self.smu2.source_enabled = False
            
            # Refresh displays
            self.refresh_all()
            
            self.status_var.set("Instruments reset")
            
        except Exception as e:
            self.status_var.set(f"Error: {e}")
    
    def refresh_all(self):
        """Refresh all instrument displays."""
        try:
            # Refresh single widget
            self.single_widget.refresh_all()
            
            # Refresh panel
            self.instrument_panel.refresh_all_instruments()
            
            self.status_var.set("All displays refreshed")
            
        except Exception as e:
            self.status_var.set(f"Error: {e}")
    
    def run(self):
        """Run the demo application."""
        try:
            self.root.mainloop()
        finally:
            # Cleanup
            self.smu1.close()
            self.smu2.close()


def demo_simple_widget():
    """Simple demonstration without full GUI."""
    print("Simple instrument widget demo")
    
    # Create a simple tkinter window
    root = tk.Tk()
    root.title("Simple Instrument Widget Demo")
    root.geometry("600x400")
    
    # Create mock instrument
    mock_adapter = MockAdapter("MOCK::KEITHLEY2400::INSTR")
    mock_adapter.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39")
    mock_adapter.add_response(":SOUR:VOLT:LEV?", "0.000000")
    mock_adapter.add_response(":SENS:CURR:PROT?", "1.050000E-03")
    mock_adapter.add_response(":SENS:VOLT:PROT?", "21.000000")
    mock_adapter.add_response(":SOUR:CURR:LEV?", "0.000000E+00")
    mock_adapter.add_response(":OUTP?", "0")
    mock_adapter.add_response(":SOUR:FUNC?", "VOLT")
    mock_adapter.add_response(":SENS:CURR:RANG?", "1.050000E-03")
    mock_adapter.add_response(":SENS:VOLT:RANG?", "21.000000")
    mock_adapter.add_response(":MEAS:VOLT?", "0.000000")
    mock_adapter.add_response(":MEAS:CURR?", "0.000000E+00")
    mock_adapter.add_response(":MEAS:RES?", "9.999910E+37")
    
    smu = Keithley2400(mock_adapter, name="Test SMU")
    
    # Create instrument widget
    widget = InstrumentWidget(root, smu, "Test Keithley 2400")
    
    # Add some instructions
    instructions = ttk.Label(
        root,
        text="Use the controls above to interact with the instrument.\nTry changing values and clicking 'Set' buttons.",
        justify=tk.CENTER
    )
    instructions.pack(pady=10)
    
    try:
        root.mainloop()
    finally:
        smu.close()


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "simple":
        demo_simple_widget()
    else:
        # Run full demo
        print("Starting instrument control demo...")
        print("Close the window to exit.")
        demo = InstrumentControlDemo()
        demo.run()
