{"framework": {"name": "Laboratory Automation Framework", "version": "0.1.0", "log_level": "INFO", "log_file": "lab_framework.log"}, "instruments": {"smu1": {"type": "Keithley2400", "adapter": {"type": "VISAAdapter", "resource": "GPIB0::24::INSTR", "timeout": 5000}, "name": "Primary SMU", "description": "Main source-measure unit for I-V characterization", "enabled": true, "auto_connect": true, "settings": {"source_function": "VOLT", "source_voltage": 0.0, "compliance_current": 0.001, "auto_range_current": true, "auto_range_voltage": true}}, "smu2": {"type": "Keithley2400", "adapter": {"type": "VISAAdapter", "resource": "GPIB0::25::INSTR", "timeout": 5000}, "name": "Secondary SMU", "description": "Secondary source-measure unit for gate bias", "enabled": false, "auto_connect": false, "settings": {"source_function": "VOLT", "source_voltage": 0.0, "compliance_current": 0.0001, "auto_range_current": true, "auto_range_voltage": true}}, "mock_smu": {"type": "Keithley2400", "adapter": {"type": "<PERSON>ck<PERSON><PERSON>pt<PERSON>", "resource": "MOCK::KEITHLEY2400::INSTR"}, "name": "Mock SMU", "description": "Mock instrument for testing and development", "enabled": true, "auto_connect": true, "settings": {"source_function": "VOLT", "source_voltage": 0.0, "compliance_current": 0.001}}}, "procedures": {"iv_sweep": {"type": "IVCurveProcedure", "name": "I-V Curve Measurement", "description": "Measure current-voltage characteristics", "enabled": true, "parameters": {"start_voltage": -1.0, "stop_voltage": 1.0, "num_points": 21, "current_limit": 0.01, "settling_time": 0.1, "measurement_delay": 0.05}, "instruments": {"smu": "smu1"}}, "gate_sweep": {"type": "GateSweepProcedure", "name": "Gate Voltage Sweep", "description": "Sweep gate voltage while measuring drain current", "enabled": false, "parameters": {"drain_voltage": 1.0, "gate_start": -2.0, "gate_stop": 2.0, "gate_points": 41, "drain_current_limit": 0.01, "gate_current_limit": 0.0001}, "instruments": {"drain_smu": "smu1", "gate_smu": "smu2"}}}, "gui": {"theme": "default", "window_size": {"width": 1200, "height": 800}, "plot_settings": {"max_points": 1000, "update_interval": 50, "auto_scale": true, "grid": true}, "auto_refresh_interval": 1000}, "data": {"output_directory": "data", "file_format": "csv", "auto_save": true, "timestamp_format": "%Y%m%d_%H%M%S", "include_metadata": true}}