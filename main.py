"""
Main entry point for the laboratory automation framework.

This script demonstrates the basic usage of the framework and provides
a simple command-line interface for testing instrument functionality.
"""

import sys
import logging
import time
from typing import Dict, Any

# Import our framework components
from instruments.base import VI<PERSON><PERSON><PERSON>pter, MockAdapter
from instruments.keithley2400 import Keithley2400

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('lab_framework.log')
    ]
)
logger = logging.getLogger(__name__)


class LabFramework:
    """
    Main laboratory automation framework class.
    
    This class manages instruments and provides a unified interface
    for laboratory automation tasks.
    """
    
    def __init__(self):
        """Initialize the framework."""
        self.instruments: Dict[str, Any] = {}
        self.config = {}
        logger.info("Laboratory automation framework initialized")
    
    def add_instrument(self, name: str, instrument: Any) -> None:
        """
        Add an instrument to the framework.
        
        Args:
            name: Unique name for the instrument
            instrument: Instrument instance
        """
        self.instruments[name] = instrument
        logger.info(f"Added instrument: {name}")
    
    def get_instrument(self, name: str) -> Any:
        """
        Get an instrument by name.
        
        Args:
            name: Name of the instrument
            
        Returns:
            The instrument instance
        """
        if name not in self.instruments:
            raise ValueError(f"Instrument '{name}' not found")
        return self.instruments[name]
    
    def list_instruments(self) -> list:
        """Get a list of all instrument names."""
        return list(self.instruments.keys())
    
    def close_all(self) -> None:
        """Close all instrument connections."""
        for name, instrument in self.instruments.items():
            try:
                instrument.close()
                logger.info(f"Closed instrument: {name}")
            except Exception as e:
                logger.error(f"Error closing instrument {name}: {e}")


def demo_basic_usage():
    """Demonstrate basic framework usage."""
    print("\n" + "="*50)
    print("Laboratory Automation Framework Demo")
    print("="*50)
    
    # Create framework instance
    framework = LabFramework()
    
    # Create a mock Keithley 2400 for demonstration
    print("\n1. Creating mock Keithley 2400...")
    mock_adapter = MockAdapter("MOCK::KEITHLEY2400::INSTR")
    
    # Add some realistic responses
    mock_adapter.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39")
    mock_adapter.add_response(":SOUR:VOLT:LEV?", "0.000000")
    mock_adapter.add_response(":SENS:CURR:PROT?", "1.050000E-03")
    mock_adapter.add_response(":OUTP?", "0")
    mock_adapter.add_response(":SOUR:FUNC?", "VOLT")
    mock_adapter.add_response(":READ?", "1.000000E+00,1.000000E-03,9.999910E+37")
    
    smu = Keithley2400(mock_adapter, name="Demo K2400")
    framework.add_instrument("smu1", smu)
    
    print(f"   Instrument ID: {smu.id}")
    print(f"   Current voltage setting: {smu.source_voltage} V")
    print(f"   Current compliance: {smu.compliance_current} A")
    
    # Demonstrate basic operations
    print("\n2. Configuring instrument...")
    smu.configure_voltage_source(voltage=2.0, current_limit=0.01)
    print(f"   Configured as voltage source: 2.0V, 10mA limit")
    
    print("\n3. Taking measurements...")
    smu.source_enabled = True
    time.sleep(0.1)  # Brief settling time
    
    voltage, current = smu.measure_iv()
    print(f"   Measured: {voltage:.3f} V, {current:.6f} A")
    
    resistance = voltage / current if current != 0 else float('inf')
    print(f"   Calculated resistance: {resistance:.1f} Ω")
    
    smu.source_enabled = False
    print("   Output disabled")
    
    # List all instruments
    print(f"\n4. Framework contains {len(framework.list_instruments())} instrument(s):")
    for name in framework.list_instruments():
        print(f"   - {name}")
    
    # Clean up
    framework.close_all()
    print("\n5. All instruments closed")
    print("\nDemo completed successfully!")


def demo_iv_sweep():
    """Demonstrate an I-V sweep measurement."""
    print("\n" + "="*50)
    print("I-V Sweep Demonstration")
    print("="*50)
    
    # Create framework and instrument
    framework = LabFramework()
    mock_adapter = MockAdapter("MOCK::KEITHLEY2400::INSTR")
    
    # Add responses for I-V sweep
    mock_adapter.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39")
    
    smu = Keithley2400(mock_adapter, name="IV Sweep K2400")
    framework.add_instrument("smu1", smu)
    
    # Define sweep parameters
    start_voltage = -1.0
    stop_voltage = 1.0
    num_points = 11
    current_limit = 0.01
    
    print(f"\nSweep parameters:")
    print(f"   Voltage range: {start_voltage} V to {stop_voltage} V")
    print(f"   Number of points: {num_points}")
    print(f"   Current limit: {current_limit} A")
    
    # Configure instrument
    smu.configure_voltage_source(voltage=0, current_limit=current_limit)
    smu.source_enabled = True
    
    # Perform sweep
    voltages = []
    currents = []
    
    print(f"\nPerforming I-V sweep...")
    print(f"{'Point':<6} {'Voltage (V)':<12} {'Current (A)':<12} {'Resistance (Ω)':<15}")
    print("-" * 50)
    
    for i in range(num_points):
        # Calculate voltage for this point
        voltage = start_voltage + (stop_voltage - start_voltage) * i / (num_points - 1)
        
        # Set voltage and take measurement
        smu.source_voltage = voltage
        time.sleep(0.05)  # Brief settling time
        
        # Simulate realistic I-V response (Ohm's law with some noise)
        resistance = 1000  # 1kΩ resistor simulation
        current = voltage / resistance + (0.001 * (0.5 - abs(voltage)))  # Add some nonlinearity
        
        # Mock the measurement response
        mock_adapter.add_response(":READ?", f"{voltage:.6f},{current:.6e},9.999910E+37")
        
        measured_v, measured_i = smu.measure_iv()
        voltages.append(measured_v)
        currents.append(measured_i)
        
        # Calculate resistance
        calc_resistance = measured_v / measured_i if measured_i != 0 else float('inf')
        
        print(f"{i+1:<6} {measured_v:<12.3f} {measured_i:<12.6e} {calc_resistance:<15.1f}")
    
    # Disable output
    smu.source_enabled = False
    
    # Calculate average resistance
    resistances = [v/i for v, i in zip(voltages, currents) if i != 0]
    avg_resistance = sum(resistances) / len(resistances) if resistances else 0
    
    print(f"\nSweep completed!")
    print(f"Average resistance: {avg_resistance:.1f} Ω")
    
    # Clean up
    framework.close_all()


def interactive_menu():
    """Provide an interactive menu for testing the framework."""
    while True:
        print("\n" + "="*50)
        print("Laboratory Automation Framework")
        print("="*50)
        print("1. Basic usage demo")
        print("2. I-V sweep demo")
        print("3. Test instrument connection")
        print("4. Exit")
        
        try:
            choice = input("\nEnter your choice (1-4): ").strip()
            
            if choice == "1":
                demo_basic_usage()
            elif choice == "2":
                demo_iv_sweep()
            elif choice == "3":
                test_instrument_connection()
            elif choice == "4":
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1, 2, 3, or 4.")
                
        except KeyboardInterrupt:
            print("\n\nExiting...")
            break
        except Exception as e:
            logger.error(f"Error in interactive menu: {e}")
            print(f"An error occurred: {e}")


def test_instrument_connection():
    """Test connection to a real instrument."""
    print("\n" + "="*50)
    print("Test Real Instrument Connection")
    print("="*50)
    
    # Common VISA addresses to try
    addresses = [
        "GPIB0::24::INSTR",
        "GPIB0::1::INSTR", 
        "USB0::0x05E6::0x2400::1234567::INSTR",
    ]
    
    print("Attempting to connect to real instruments...")
    
    for address in addresses:
        try:
            print(f"\nTrying {address}...")
            visa_adapter = VISAAdapter(address, timeout=2000)
            smu = Keithley2400(visa_adapter, name="Real K2400")
            
            print(f"   SUCCESS! Connected to: {smu.id}")
            print(f"   Current settings:")
            print(f"     Source voltage: {smu.source_voltage} V")
            print(f"     Compliance current: {smu.compliance_current} A")
            print(f"     Output enabled: {smu.source_enabled}")
            
            smu.close()
            return
            
        except Exception as e:
            print(f"   Failed: {e}")
    
    print("\nNo instruments found. Using mock adapter for demonstration.")
    demo_basic_usage()


def launch_gui():
    """Launch the integrated GUI application."""
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox
        from gui.widgets.plotting import RealTimePlotWidget, MultiChannelPlotWidget
        from gui.widgets.instrument_control import InstrumentPanel
        from config_manager import get_config_manager

        print("Starting Laboratory Automation Framework GUI...")

        class LabFrameworkGUI:
            """Main GUI application for the laboratory automation framework."""

            def __init__(self):
                """Initialize the GUI application."""
                self.root = tk.Tk()
                self.root.title("Laboratory Automation Framework")

                # Get configuration
                self.config_mgr = get_config_manager()
                gui_config = self.config_mgr.get_gui_config()
                window_size = gui_config.get("window_size", {"width": 1200, "height": 800})

                self.root.geometry(f"{window_size['width']}x{window_size['height']}")

                # Initialize components
                self.instruments = {}
                self.plots = {}

                # Setup GUI
                self._setup_gui()
                self._load_instruments()

            def _setup_gui(self):
                """Setup the main GUI layout."""
                # Create main paned window
                self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
                self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

                # Left panel for instrument control
                self.left_frame = ttk.Frame(self.main_paned)
                self.main_paned.add(self.left_frame, weight=1)

                # Right panel for plotting
                self.right_frame = ttk.Frame(self.main_paned)
                self.main_paned.add(self.right_frame, weight=2)

                # Setup instrument panel
                self.instrument_panel = InstrumentPanel(self.left_frame)

                # Setup plotting area
                self._setup_plotting_area()

                # Setup menu and toolbar
                self._setup_menu()
                self._setup_toolbar()

            def _setup_plotting_area(self):
                """Setup the plotting area."""
                # Create notebook for different plots
                self.plot_notebook = ttk.Notebook(self.right_frame)
                self.plot_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

                # Real-time plot tab
                self.realtime_frame = ttk.Frame(self.plot_notebook)
                self.plot_notebook.add(self.realtime_frame, text="Real-time Data")

                self.realtime_plot = RealTimePlotWidget(
                    parent=self.realtime_frame,
                    max_points=1000,
                    update_interval=100
                )
                self.realtime_plot.set_labels("Time (s)", "Value", "Real-time Measurements")

                # Multi-channel plot tab
                self.multichannel_frame = ttk.Frame(self.plot_notebook)
                self.plot_notebook.add(self.multichannel_frame, text="Multi-channel")

                self.multichannel_plot = MultiChannelPlotWidget(
                    parent=self.multichannel_frame,
                    num_channels=3,
                    max_points=1000,
                    update_interval=100
                )
                self.multichannel_plot.set_channel_label(0, "Voltage (V)")
                self.multichannel_plot.set_channel_label(1, "Current (A)")
                self.multichannel_plot.set_channel_label(2, "Power (W)")

            def _setup_menu(self):
                """Setup the menu bar."""
                self.menubar = tk.Menu(self.root)
                self.root.config(menu=self.menubar)

                # File menu
                file_menu = tk.Menu(self.menubar, tearoff=0)
                self.menubar.add_cascade(label="File", menu=file_menu)
                file_menu.add_command(label="Load Configuration", command=self._load_config)
                file_menu.add_command(label="Save Configuration", command=self._save_config)
                file_menu.add_separator()
                file_menu.add_command(label="Exit", command=self._on_closing)

                # Instruments menu
                instruments_menu = tk.Menu(self.menubar, tearoff=0)
                self.menubar.add_cascade(label="Instruments", menu=instruments_menu)
                instruments_menu.add_command(label="Refresh All", command=self._refresh_instruments)
                instruments_menu.add_command(label="Reconnect All", command=self._reconnect_instruments)

                # View menu
                view_menu = tk.Menu(self.menubar, tearoff=0)
                self.menubar.add_cascade(label="View", menu=view_menu)
                view_menu.add_command(label="Start Plotting", command=self._start_plotting)
                view_menu.add_command(label="Stop Plotting", command=self._stop_plotting)
                view_menu.add_command(label="Clear Plots", command=self._clear_plots)

                # Help menu
                help_menu = tk.Menu(self.menubar, tearoff=0)
                self.menubar.add_cascade(label="Help", menu=help_menu)
                help_menu.add_command(label="About", command=self._show_about)

            def _setup_toolbar(self):
                """Setup the toolbar."""
                self.toolbar = ttk.Frame(self.root)
                self.toolbar.pack(fill=tk.X, padx=5, pady=2)

                # Status label
                self.status_var = tk.StringVar(value="Ready")
                status_label = ttk.Label(self.toolbar, textvariable=self.status_var)
                status_label.pack(side=tk.RIGHT, padx=5)

                # Quick action buttons
                ttk.Button(self.toolbar, text="Refresh", command=self._refresh_instruments).pack(side=tk.LEFT, padx=2)
                ttk.Button(self.toolbar, text="Start Plot", command=self._start_plotting).pack(side=tk.LEFT, padx=2)
                ttk.Button(self.toolbar, text="Stop Plot", command=self._stop_plotting).pack(side=tk.LEFT, padx=2)

            def _load_instruments(self):
                """Load instruments from configuration."""
                try:
                    self.status_var.set("Loading instruments...")
                    self.instruments = self.config_mgr.load_instruments(auto_connect=True)

                    # Add instruments to the panel
                    for name, instrument in self.instruments.items():
                        self.instrument_panel.add_instrument(name, instrument)

                    self.status_var.set(f"Loaded {len(self.instruments)} instrument(s)")

                except Exception as e:
                    logger.error(f"Error loading instruments: {e}")
                    self.status_var.set(f"Error loading instruments: {e}")
                    messagebox.showerror("Error", f"Failed to load instruments:\n{e}")

            def _load_config(self):
                """Load configuration from file."""
                try:
                    self.config_mgr.load_config()
                    self.status_var.set("Configuration reloaded")
                    messagebox.showinfo("Success", "Configuration reloaded successfully")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to load configuration:\n{e}")

            def _save_config(self):
                """Save configuration to file."""
                try:
                    self.config_mgr.save_config()
                    self.status_var.set("Configuration saved")
                    messagebox.showinfo("Success", "Configuration saved successfully")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to save configuration:\n{e}")

            def _refresh_instruments(self):
                """Refresh all instrument displays."""
                try:
                    self.instrument_panel.refresh_all_instruments()
                    self.status_var.set("Instruments refreshed")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to refresh instruments:\n{e}")

            def _reconnect_instruments(self):
                """Reconnect all instruments."""
                try:
                    self.status_var.set("Reconnecting instruments...")
                    self.config_mgr.close_all_instruments()
                    self._load_instruments()
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to reconnect instruments:\n{e}")

            def _start_plotting(self):
                """Start real-time plotting."""
                try:
                    self.realtime_plot.start_animation()
                    self.multichannel_plot.start_animation()
                    self.status_var.set("Plotting started")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to start plotting:\n{e}")

            def _stop_plotting(self):
                """Stop real-time plotting."""
                try:
                    self.realtime_plot.stop_animation()
                    self.multichannel_plot.stop_animation()
                    self.status_var.set("Plotting stopped")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to stop plotting:\n{e}")

            def _clear_plots(self):
                """Clear all plot data."""
                try:
                    self.realtime_plot.clear_data()
                    self.multichannel_plot.clear_data()
                    self.status_var.set("Plots cleared")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to clear plots:\n{e}")

            def _show_about(self):
                """Show about dialog."""
                framework_config = self.config_mgr.get_framework_config()
                about_text = f"""
{framework_config.get('name', 'Laboratory Automation Framework')}
Version: {framework_config.get('version', '0.1.0')}

A comprehensive framework for laboratory instrument control
and data acquisition.

Features:
• Dynamic instrument loading from configuration
• Real-time data plotting
• Modular instrument drivers
• Extensible procedure system
                """
                messagebox.showinfo("About", about_text.strip())

            def _on_closing(self):
                """Handle application closing."""
                try:
                    self._stop_plotting()
                    self.config_mgr.close_all_instruments()
                    self.root.destroy()
                except Exception as e:
                    logger.error(f"Error during shutdown: {e}")
                    self.root.destroy()

            def run(self):
                """Run the GUI application."""
                self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
                self.root.mainloop()

        # Create and run the GUI
        app = LabFrameworkGUI()
        app.run()

    except ImportError as e:
        print(f"GUI dependencies not available: {e}")
        print("Please install GUI dependencies or run in command-line mode")
        print("Falling back to command-line interface...")
        interactive_menu()
    except Exception as e:
        logger.error(f"Error launching GUI: {e}")
        print(f"Error launching GUI: {e}")
        print("Falling back to command-line interface...")
        interactive_menu()


if __name__ == "__main__":
    try:
        if len(sys.argv) > 1:
            if sys.argv[1] == "demo":
                demo_basic_usage()
            elif sys.argv[1] == "sweep":
                demo_iv_sweep()
            elif sys.argv[1] == "test":
                test_instrument_connection()
            elif sys.argv[1] == "gui":
                launch_gui()
            elif sys.argv[1] == "cli":
                interactive_menu()
            else:
                print("Usage: python main.py [demo|sweep|test|gui|cli]")
        else:
            # Default to GUI if available, otherwise CLI
            launch_gui()

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
        sys.exit(1)
