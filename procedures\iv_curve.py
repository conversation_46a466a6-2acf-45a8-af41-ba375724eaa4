"""
I-V curve measurement procedure.

This module implements an automated I-V curve measurement procedure
for characterizing electronic devices and materials.
"""

import time
import logging
import numpy as np
from typing import Dict, Any, Optional
from .base import BaseProcedure, DataPoint

logger = logging.getLogger(__name__)


class IVCurveProcedure(BaseProcedure):
    """
    I-V curve measurement procedure.
    
    This procedure performs automated current-voltage characterization
    by sweeping voltage and measuring the resulting current.
    """
    
    def __init__(self, name: str = "I-V Curve", description: str = "Current-voltage characterization"):
        """Initialize the I-V curve procedure."""
        super().__init__(name, description)
        
        # Procedure parameters
        self.start_voltage = -1.0
        self.stop_voltage = 1.0
        self.num_points = 21
        self.current_limit = 0.01
        self.settling_time = 0.1
        self.measurement_delay = 0.05
        
        # Instruments
        self.smu = None
        
        # Voltage points
        self.voltage_points = []
        
    def setup(self, **kwargs) -> None:
        """
        Setup the I-V curve procedure.
        
        Args:
            smu: Source-measure unit instrument
            start_voltage: Starting voltage (V)
            stop_voltage: Ending voltage (V)
            num_points: Number of measurement points
            current_limit: Current compliance limit (A)
            settling_time: Time to wait after setting voltage (s)
            measurement_delay: Additional delay before measurement (s)
        """
        # Get required instrument
        self.smu = kwargs.get('smu')
        if self.smu is None:
            raise ValueError("SMU instrument is required")
        
        # Set parameters
        self.start_voltage = kwargs.get('start_voltage', self.start_voltage)
        self.stop_voltage = kwargs.get('stop_voltage', self.stop_voltage)
        self.num_points = kwargs.get('num_points', self.num_points)
        self.current_limit = kwargs.get('current_limit', self.current_limit)
        self.settling_time = kwargs.get('settling_time', self.settling_time)
        self.measurement_delay = kwargs.get('measurement_delay', self.measurement_delay)
        
        # Validate parameters
        if self.num_points < 2:
            raise ValueError("Number of points must be at least 2")
        
        if self.current_limit <= 0:
            raise ValueError("Current limit must be positive")
        
        # Generate voltage points
        self.voltage_points = np.linspace(
            self.start_voltage, 
            self.stop_voltage, 
            self.num_points
        ).tolist()
        
        # Configure the SMU
        self.smu.configure_voltage_source(
            voltage=self.start_voltage,
            current_limit=self.current_limit
        )
        
        # Enable auto-ranging for better accuracy
        if hasattr(self.smu, 'auto_range_current'):
            self.smu.auto_range_current(True)
        if hasattr(self.smu, 'auto_range_voltage'):
            self.smu.auto_range_voltage(True)
        
        logger.info(f"I-V curve setup: {self.start_voltage}V to {self.stop_voltage}V, "
                   f"{self.num_points} points, {self.current_limit}A limit")
    
    def get_total_steps(self) -> int:
        """Get the total number of steps."""
        return len(self.voltage_points) + 2  # +2 for enable/disable output
    
    def execute_step(self, step: int) -> DataPoint:
        """
        Execute a single step of the I-V curve measurement.
        
        Args:
            step: Step number
            
        Returns:
            DataPoint with measurement data
        """
        timestamp = time.time()
        
        if step == 0:
            # Step 0: Enable output
            self.smu.source_enabled = True
            time.sleep(self.settling_time)
            
            return DataPoint(
                timestamp=timestamp,
                step=step,
                parameters={'action': 'enable_output'},
                measurements={},
                metadata={'description': 'Enable SMU output'}
            )
        
        elif step == len(self.voltage_points) + 1:
            # Final step: Disable output
            self.smu.source_enabled = False
            
            return DataPoint(
                timestamp=timestamp,
                step=step,
                parameters={'action': 'disable_output'},
                measurements={},
                metadata={'description': 'Disable SMU output'}
            )
        
        else:
            # Measurement steps
            voltage_index = step - 1
            target_voltage = self.voltage_points[voltage_index]
            
            # Set voltage
            self.smu.source_voltage = target_voltage
            time.sleep(self.settling_time)
            
            # Additional measurement delay
            if self.measurement_delay > 0:
                time.sleep(self.measurement_delay)
            
            # Take measurement
            measured_voltage, measured_current = self.smu.measure_iv()
            
            # Calculate additional parameters
            power = measured_voltage * measured_current

            # Calculate resistance with proper handling of zero current
            if abs(measured_current) > 1e-12:
                resistance = measured_voltage / measured_current
            else:
                resistance = float('inf') if measured_voltage != 0 else 0.0

            # Calculate conductance
            if resistance != float('inf') and resistance != 0:
                conductance = 1.0 / resistance
            else:
                conductance = 0.0
            
            # Check for compliance
            compliance = abs(measured_current) >= (0.95 * self.current_limit)
            
            return DataPoint(
                timestamp=timestamp,
                step=step,
                parameters={
                    'target_voltage': target_voltage,
                    'voltage_index': voltage_index,
                    'current_limit': self.current_limit
                },
                measurements={
                    'voltage': measured_voltage,
                    'current': measured_current,
                    'power': power,
                    'resistance': resistance,
                    'conductance': conductance
                },
                metadata={
                    'compliance': compliance,
                    'settling_time': self.settling_time,
                    'measurement_delay': self.measurement_delay
                }
            )
    
    def cleanup(self) -> None:
        """Cleanup after the procedure."""
        try:
            if self.smu:
                # Ensure output is disabled
                self.smu.source_enabled = False
                # Set voltage to zero
                self.smu.source_voltage = 0.0
                logger.info("I-V curve cleanup completed")
        except Exception as e:
            logger.error(f"Error during I-V curve cleanup: {e}")
    
    def get_iv_data(self) -> tuple:
        """
        Extract I-V data from collected data points.
        
        Returns:
            Tuple of (voltages, currents) arrays
        """
        voltages = []
        currents = []
        
        for data_point in self.data_points:
            if 'voltage' in data_point.measurements and 'current' in data_point.measurements:
                voltages.append(data_point.measurements['voltage'])
                currents.append(data_point.measurements['current'])
        
        return np.array(voltages), np.array(currents)
    
    def get_power_data(self) -> tuple:
        """
        Extract power data from collected data points.
        
        Returns:
            Tuple of (voltages, powers) arrays
        """
        voltages = []
        powers = []
        
        for data_point in self.data_points:
            if 'voltage' in data_point.measurements and 'power' in data_point.measurements:
                voltages.append(data_point.measurements['voltage'])
                powers.append(data_point.measurements['power'])
        
        return np.array(voltages), np.array(powers)
    
    def get_resistance_data(self) -> tuple:
        """
        Extract resistance data from collected data points.
        
        Returns:
            Tuple of (voltages, resistances) arrays
        """
        voltages = []
        resistances = []
        
        for data_point in self.data_points:
            if 'voltage' in data_point.measurements and 'resistance' in data_point.measurements:
                resistance = data_point.measurements['resistance']
                if resistance != float('inf'):  # Filter out infinite resistance values
                    voltages.append(data_point.measurements['voltage'])
                    resistances.append(resistance)
        
        return np.array(voltages), np.array(resistances)
    
    def analyze_results(self) -> Dict[str, Any]:
        """
        Analyze the I-V curve results.
        
        Returns:
            Dictionary with analysis results
        """
        if not self.data_points:
            return {}
        
        voltages, currents = self.get_iv_data()
        
        if len(voltages) == 0:
            return {}
        
        analysis = {
            'num_points': len(voltages),
            'voltage_range': (float(np.min(voltages)), float(np.max(voltages))),
            'current_range': (float(np.min(currents)), float(np.max(currents))),
            'max_power': float(np.max(np.abs(voltages * currents))),
        }
        
        # Calculate average resistance (excluding infinite values)
        _, resistances = self.get_resistance_data()
        if len(resistances) > 0:
            analysis['avg_resistance'] = float(np.mean(resistances))
            analysis['resistance_std'] = float(np.std(resistances))
        
        # Check for compliance events
        compliance_count = sum(1 for dp in self.data_points 
                             if dp.metadata.get('compliance', False))
        analysis['compliance_events'] = compliance_count
        
        # Linear fit for resistance estimation (if data is roughly linear)
        if len(voltages) > 2:
            try:
                # Fit I = V/R (linear relationship)
                slope, intercept = np.polyfit(voltages, currents, 1)
                analysis['linear_resistance'] = float(1.0 / slope) if slope != 0 else float('inf')
                analysis['linear_fit_quality'] = float(np.corrcoef(voltages, currents)[0, 1]**2)
            except:
                pass
        
        return analysis
    
    def save_data(self, filename: str, include_analysis: bool = True) -> None:
        """
        Save the I-V curve data to a file.
        
        Args:
            filename: Output filename
            include_analysis: Whether to include analysis results
        """
        import csv
        import json
        
        # Determine file format from extension
        if filename.endswith('.json'):
            # Save as JSON
            data = {
                'procedure': {
                    'name': self.name,
                    'description': self.description,
                    'parameters': {
                        'start_voltage': self.start_voltage,
                        'stop_voltage': self.stop_voltage,
                        'num_points': self.num_points,
                        'current_limit': self.current_limit,
                        'settling_time': self.settling_time,
                        'measurement_delay': self.measurement_delay
                    }
                },
                'data_points': [
                    {
                        'timestamp': dp.timestamp,
                        'step': dp.step,
                        'parameters': dp.parameters,
                        'measurements': dp.measurements,
                        'metadata': dp.metadata
                    }
                    for dp in self.data_points
                ]
            }
            
            if include_analysis:
                data['analysis'] = self.analyze_results()
            
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
        
        else:
            # Save as CSV
            voltages, currents = self.get_iv_data()
            
            with open(filename, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['Voltage (V)', 'Current (A)', 'Power (W)', 'Resistance (Ohm)'])
                
                for v, i in zip(voltages, currents):
                    power = v * i
                    resistance = v / i if abs(i) > 1e-12 else float('inf')
                    writer.writerow([v, i, power, resistance])
        
        logger.info(f"I-V curve data saved to {filename}")
