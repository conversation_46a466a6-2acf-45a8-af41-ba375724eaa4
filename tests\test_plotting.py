"""
Test script for plotting widgets.

This script demonstrates the real-time plotting capabilities
of the laboratory automation framework.
"""

import sys
import os
import numpy as np
import time
import threading
import tkinter as tk
from tkinter import ttk

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.widgets.plotting import RealTimePlotWidget, MultiChannelPlotWidget


class PlottingDemo:
    """Demonstration application for plotting widgets."""
    
    def __init__(self):
        """Initialize the demo application."""
        self.root = tk.Tk()
        self.root.title("Laboratory Automation Framework - Plotting Demo")
        self.root.geometry("1200x800")
        
        # Data generation control
        self.is_generating = False
        self.data_thread = None
        
        # Setup the GUI
        self._setup_gui()
        
    def _setup_gui(self):
        """Setup the GUI layout."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Single channel plot tab
        self.single_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.single_frame, text="Single Channel")
        
        # Multi-channel plot tab
        self.multi_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.multi_frame, text="Multi Channel")
        
        # Setup single channel plot
        self._setup_single_channel()
        
        # Setup multi-channel plot
        self._setup_multi_channel()
        
        # Control panel
        self._setup_controls()
    
    def _setup_single_channel(self):
        """Setup single channel plotting demo."""
        # Create plot widget
        self.single_plot = RealTimePlotWidget(
            parent=self.single_frame,
            max_points=500,
            update_interval=50
        )
        
        # Configure plot
        self.single_plot.set_labels(
            xlabel="Time (s)",
            ylabel="Voltage (V)",
            title="Real-time Voltage Measurement"
        )
        self.single_plot.set_line_style(color='blue', width=2.0)
    
    def _setup_multi_channel(self):
        """Setup multi-channel plotting demo."""
        # Create plot widget
        self.multi_plot = MultiChannelPlotWidget(
            parent=self.multi_frame,
            num_channels=3,
            max_points=500,
            update_interval=50
        )
        
        # Configure plot
        self.multi_plot.set_channel_label(0, "Voltage (V)")
        self.multi_plot.set_channel_label(1, "Current (A)")
        self.multi_plot.set_channel_label(2, "Power (W)")
        
        self.multi_plot.ax.set_xlabel("Time (s)")
        self.multi_plot.ax.set_ylabel("Value")
        self.multi_plot.ax.set_title("Multi-channel Real-time Measurements")
    
    def _setup_controls(self):
        """Setup control panel."""
        # Control frame
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Start/Stop button
        self.start_button = ttk.Button(
            control_frame,
            text="Start Data Generation",
            command=self.toggle_data_generation
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        # Clear button
        clear_button = ttk.Button(
            control_frame,
            text="Clear Data",
            command=self.clear_data
        )
        clear_button.pack(side=tk.LEFT, padx=5)
        
        # Save button
        save_button = ttk.Button(
            control_frame,
            text="Save Plots",
            command=self.save_plots
        )
        save_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_label = ttk.Label(
            control_frame,
            text="Status: Ready"
        )
        self.status_label.pack(side=tk.RIGHT, padx=5)
    
    def toggle_data_generation(self):
        """Start or stop data generation."""
        if not self.is_generating:
            self.start_data_generation()
        else:
            self.stop_data_generation()
    
    def start_data_generation(self):
        """Start generating simulated data."""
        self.is_generating = True
        self.start_button.config(text="Stop Data Generation")
        self.status_label.config(text="Status: Generating data...")
        
        # Start animation
        self.single_plot.start_animation()
        self.multi_plot.start_animation()
        
        # Start data generation thread
        self.data_thread = threading.Thread(target=self._generate_data, daemon=True)
        self.data_thread.start()
    
    def stop_data_generation(self):
        """Stop generating data."""
        self.is_generating = False
        self.start_button.config(text="Start Data Generation")
        self.status_label.config(text="Status: Stopped")
        
        # Stop animation
        self.single_plot.stop_animation()
        self.multi_plot.stop_animation()
    
    def _generate_data(self):
        """Generate simulated measurement data."""
        start_time = time.time()
        
        while self.is_generating:
            current_time = time.time() - start_time
            
            # Generate simulated data
            # Single channel: sine wave with noise
            voltage = 5.0 * np.sin(2 * np.pi * 0.5 * current_time) + 0.1 * np.random.randn()
            
            # Multi-channel: related measurements
            current = voltage / 1000.0 + 0.001 * np.random.randn()  # V/R + noise
            power = voltage * current
            
            # Add data to plots
            self.single_plot.add_point(current_time, voltage)
            self.multi_plot.add_point(0, current_time, voltage)
            self.multi_plot.add_point(1, current_time, current)
            self.multi_plot.add_point(2, current_time, power)
            
            # Sleep for a short time to simulate real measurement rate
            time.sleep(0.1)  # 10 Hz update rate
    
    def clear_data(self):
        """Clear all plot data."""
        self.single_plot.clear_data()
        self.multi_plot.clear_data()
        self.status_label.config(text="Status: Data cleared")
    
    def save_plots(self):
        """Save current plots to files."""
        try:
            self.single_plot.save_plot("single_channel_plot.png")
            self.multi_plot.save_plot("multi_channel_plot.png")
            self.status_label.config(text="Status: Plots saved")
        except Exception as e:
            self.status_label.config(text=f"Status: Error saving plots - {e}")
    
    def run(self):
        """Run the demo application."""
        try:
            self.root.mainloop()
        finally:
            # Cleanup
            self.stop_data_generation()


def demo_simple_plot():
    """Simple demonstration without GUI."""
    print("Simple plotting demo (no GUI)")
    print("Generating sample data...")
    
    # Create a simple plot widget
    plot = RealTimePlotWidget(max_points=100)
    plot.set_labels("Time (s)", "Signal (V)", "Test Signal")
    
    # Generate some test data
    t = np.linspace(0, 10, 100)
    signal = np.sin(t) + 0.1 * np.random.randn(100)
    
    # Add data points
    for time_val, signal_val in zip(t, signal):
        plot.add_point(time_val, signal_val)
    
    # Update plot once
    plot._update_plot(0)
    
    # Save the plot
    plot.save_plot("test_plot.png")
    print("Plot saved as test_plot.png")


def demo_multi_channel():
    """Multi-channel demonstration without GUI."""
    print("Multi-channel plotting demo (no GUI)")
    print("Generating sample data...")
    
    # Create multi-channel plot
    plot = MultiChannelPlotWidget(num_channels=2, max_points=100)
    plot.set_channel_label(0, "Channel A")
    plot.set_channel_label(1, "Channel B")
    
    # Generate test data
    t = np.linspace(0, 10, 100)
    signal_a = np.sin(t)
    signal_b = np.cos(t)
    
    # Add data points
    for time_val, sig_a, sig_b in zip(t, signal_a, signal_b):
        plot.add_point(0, time_val, sig_a)
        plot.add_point(1, time_val, sig_b)
    
    # Update plot once
    plot._update_plot(0)
    
    # Save the plot
    plot.save_plot("test_multi_plot.png")
    print("Multi-channel plot saved as test_multi_plot.png")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "simple":
            demo_simple_plot()
        elif sys.argv[1] == "multi":
            demo_multi_channel()
        else:
            print("Usage: python test_plotting.py [simple|multi]")
    else:
        # Run full GUI demo
        print("Starting GUI plotting demo...")
        print("Close the window to exit.")
        demo = PlottingDemo()
        demo.run()
