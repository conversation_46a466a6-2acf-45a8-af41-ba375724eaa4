"""
Keithley 2400 SourceMeter driver.

This module provides a driver for the Keithley 2400 series SourceMeter instruments,
which can source voltage or current and measure the resulting current or voltage.
"""

import logging
from typing import Union
from .base import Instrument, Control, Measurement

logger = logging.getLogger(__name__)


class Keithley2400(Instrument):
    """
    Driver for Keithley 2400 SourceMeter.
    
    The Keithley 2400 is a precision source-measure unit that can source voltage
    or current and simultaneously measure the resulting current or voltage.
    
    Example usage:
        >>> from instruments.base import VISAAdapter
        >>> adapter = VISAAdapter("GPIB0::24::INSTR")
        >>> smu = Keithley2400(adapter)
        >>> smu.source_voltage = 1.0  # Set source voltage to 1V
        >>> smu.compliance_current = 0.001  # Set current compliance to 1mA
        >>> smu.source_enabled = True  # Enable output
        >>> current = smu.current  # Read measured current
        >>> smu.source_enabled = False  # Disable output
    """
    
    def __init__(self, adapter: Union[str, object], name: str = "<PERSON><PERSON> 2400", **kwargs):
        """
        Initialize the Keithley 2400 instrument.
        
        Args:
            adapter: Communication adapter or resource string
            name: Human-readable name for the instrument
            **kwargs: Additional configuration parameters
        """
        super().__init__(adapter, name, **kwargs)
        
        # Initialize instrument to known state
        self.reset()
        self.clear()
        
        # Set up basic configuration
        self.write(":SENS:FUNC:CONC OFF")  # Turn off concurrent functions
        self.write(":SOUR:CLE:AUTO ON")    # Auto output-off on compliance
        self.write(":FORM:ELEM VOLT,CURR") # Set data format
        
        logger.info(f"Initialized {self.name}: {self.id}")
    
    # Source voltage control
    source_voltage = Control(
        get_command=":SOUR:VOLT:LEV?",
        set_command=":SOUR:VOLT:LEV {:.6f}",
        doc="""
        Source voltage level in volts.
        
        This property controls the voltage output level of the source.
        Range: -210V to +210V (model dependent)
        """,
        get_process=float,
        set_process=float
    )
    
    # Current compliance (protection) setting
    compliance_current = Control(
        get_command=":SENS:CURR:PROT?",
        set_command=":SENS:CURR:PROT {:.6e}",
        doc="""
        Current compliance (protection) level in amperes.
        
        This sets the maximum current that can flow when sourcing voltage.
        If this limit is exceeded, the instrument will go into compliance.
        Range: 1nA to 1A (model dependent)
        """,
        get_process=float,
        set_process=float
    )
    
    # Voltage compliance (protection) setting  
    compliance_voltage = Control(
        get_command=":SENS:VOLT:PROT?",
        set_command=":SENS:VOLT:PROT {:.6f}",
        doc="""
        Voltage compliance (protection) level in volts.
        
        This sets the maximum voltage that can be measured when sourcing current.
        Range: 200mV to 210V (model dependent)
        """,
        get_process=float,
        set_process=float
    )
    
    # Source current control
    source_current = Control(
        get_command=":SOUR:CURR:LEV?",
        set_command=":SOUR:CURR:LEV {:.6e}",
        doc="""
        Source current level in amperes.
        
        This property controls the current output level of the source.
        Range: -1A to +1A (model dependent)
        """,
        get_process=float,
        set_process=float
    )
    
    # Output enable/disable
    source_enabled = Control(
        get_command=":OUTP?",
        set_command=":OUTP {}",
        doc="""
        Enable or disable the source output.
        
        When True, the output is enabled and the instrument will source
        the configured voltage or current. When False, the output is disabled.
        """,
        get_process=lambda x: bool(int(x)),
        set_process=lambda x: "ON" if x else "OFF"
    )
    
    # Source function (voltage or current)
    source_function = Control(
        get_command=":SOUR:FUNC?",
        set_command=":SOUR:FUNC {}",
        doc="""
        Source function: 'VOLT' for voltage source, 'CURR' for current source.
        
        This determines whether the instrument sources voltage or current.
        """,
        values=["VOLT", "CURR"]
    )
    
    # Measurement functions
    voltage = Measurement(
        get_command=":MEAS:VOLT?",
        doc="""
        Measured voltage in volts.
        
        This performs a single voltage measurement and returns the result.
        """,
        get_process=float
    )
    
    current = Measurement(
        get_command=":MEAS:CURR?",
        doc="""
        Measured current in amperes.
        
        This performs a single current measurement and returns the result.
        """,
        get_process=float
    )
    
    resistance = Measurement(
        get_command=":MEAS:RES?",
        doc="""
        Measured resistance in ohms.
        
        This performs a single resistance measurement and returns the result.
        """,
        get_process=float
    )
    
    # Current measurement range
    current_range = Control(
        get_command=":SENS:CURR:RANG?",
        set_command=":SENS:CURR:RANG {:.6e}",
        doc="""
        Current measurement range in amperes.
        
        Sets the current measurement range. Use 'auto' for automatic ranging.
        Available ranges: 1e-9, 1e-8, 1e-7, 1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1, 1
        """,
        get_process=float,
        set_process=float
    )
    
    # Voltage measurement range
    voltage_range = Control(
        get_command=":SENS:VOLT:RANG?",
        set_command=":SENS:VOLT:RANG {:.6f}",
        doc="""
        Voltage measurement range in volts.
        
        Sets the voltage measurement range. Use 'auto' for automatic ranging.
        Available ranges: 0.2, 2, 20, 200
        """,
        get_process=float,
        set_process=float
    )
    
    def check_errors(self):
        """Check for instrument errors."""
        try:
            error = self.query(":SYST:ERR?")
            if not error.startswith("0,"):
                logger.warning(f"{self.name} error: {error}")
                return error
        except Exception as e:
            logger.error(f"Error checking {self.name} errors: {e}")
        return None
    
    def configure_voltage_source(self, voltage: float, current_limit: float):
        """
        Configure the instrument as a voltage source.
        
        Args:
            voltage: Source voltage in volts
            current_limit: Current compliance limit in amperes
        """
        self.source_function = "VOLT"
        self.source_voltage = voltage
        self.compliance_current = current_limit
        logger.info(f"Configured {self.name} as voltage source: {voltage}V, {current_limit}A limit")
    
    def configure_current_source(self, current: float, voltage_limit: float):
        """
        Configure the instrument as a current source.
        
        Args:
            current: Source current in amperes
            voltage_limit: Voltage compliance limit in volts
        """
        self.source_function = "CURR"
        self.source_current = current
        self.compliance_voltage = voltage_limit
        logger.info(f"Configured {self.name} as current source: {current}A, {voltage_limit}V limit")
    
    def measure_iv(self):
        """
        Perform a simultaneous current and voltage measurement.
        
        Returns:
            tuple: (voltage, current) in (volts, amperes)
        """
        # Trigger a measurement and read both voltage and current
        result = self.query(":READ?")
        values = [float(x) for x in result.split(',')]
        
        if len(values) >= 2:
            voltage, current = values[0], values[1]
            logger.debug(f"{self.name} I-V measurement: {voltage}V, {current}A")
            return voltage, current
        else:
            raise ValueError(f"Unexpected measurement result: {result}")
    
    def auto_range_current(self, enabled: bool = True):
        """Enable or disable automatic current ranging."""
        self.write(f":SENS:CURR:RANG:AUTO {'ON' if enabled else 'OFF'}")
        
    def auto_range_voltage(self, enabled: bool = True):
        """Enable or disable automatic voltage ranging."""
        self.write(f":SENS:VOLT:RANG:AUTO {'ON' if enabled else 'OFF'}")
    
    def beep(self, frequency: float = 2000, duration: float = 0.5):
        """
        Generate a beep sound.
        
        Args:
            frequency: Beep frequency in Hz (65 to 2000)
            duration: Beep duration in seconds (0 to 7.9)
        """
        self.write(f":SYST:BEEP {frequency}, {duration}")
    
    def local(self):
        """Return the instrument to local (front panel) control."""
        self.write(":SYST:LOC")
    
    def remote(self):
        """Set the instrument to remote (computer) control."""
        self.write(":SYST:REM")
