"""
Base classes for instrument communication and control.

This module provides the foundation for the laboratory automation framework,
including abstract adapter classes for communication and base instrument classes
for device control.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Union, Optional
import pyvisa
import time

# Configure logging
logger = logging.getLogger(__name__)


class Adapter(ABC):
    """
    Abstract base class for instrument communication adapters.
    
    This class defines the interface that all communication adapters must implement
    to provide a consistent way to communicate with instruments regardless of the
    underlying communication protocol (VISA, Serial, TCP/IP, etc.).
    """
    
    def __init__(self, resource_name: str, **kwargs):
        """
        Initialize the adapter.
        
        Args:
            resource_name: The resource identifier for the instrument
            **kwargs: Additional configuration parameters
        """
        self.resource_name = resource_name
        self.is_connected = False
        
    @abstractmethod
    def connect(self) -> None:
        """Establish connection to the instrument."""
        pass
        
    @abstractmethod
    def disconnect(self) -> None:
        """Close connection to the instrument."""
        pass
        
    @abstractmethod
    def write(self, command: str) -> None:
        """
        Send a command to the instrument.
        
        Args:
            command: The command string to send
        """
        pass
        
    @abstractmethod
    def read(self) -> str:
        """
        Read a response from the instrument.
        
        Returns:
            The response string from the instrument
        """
        pass
        
    @abstractmethod
    def query(self, command: str) -> str:
        """
        Send a command and read the response.
        
        Args:
            command: The command string to send
            
        Returns:
            The response string from the instrument
        """
        pass
        
    def close(self) -> None:
        """Close the connection (alias for disconnect)."""
        self.disconnect()
        
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


class VISAAdapter(Adapter):
    """
    VISA-based communication adapter.
    
    This adapter uses PyVISA to communicate with instruments that support
    the VISA (Virtual Instrument Software Architecture) standard.
    """
    
    def __init__(self, resource_name: str, timeout: float = 5000, **kwargs):
        """
        Initialize the VISA adapter.
        
        Args:
            resource_name: VISA resource string (e.g., 'GPIB0::1::INSTR')
            timeout: Communication timeout in milliseconds
            **kwargs: Additional VISA configuration parameters
        """
        super().__init__(resource_name, **kwargs)
        self.timeout = timeout
        self.visa_kwargs = kwargs
        self.resource_manager = None
        self.instrument = None
        
    def connect(self) -> None:
        """Establish VISA connection to the instrument."""
        try:
            if self.resource_manager is None:
                self.resource_manager = pyvisa.ResourceManager()
            
            self.instrument = self.resource_manager.open_resource(
                self.resource_name,
                timeout=self.timeout,
                **self.visa_kwargs
            )
            
            self.is_connected = True
            logger.info(f"Connected to {self.resource_name}")
            
        except Exception as e:
            logger.error(f"Failed to connect to {self.resource_name}: {e}")
            raise ConnectionError(f"Could not connect to {self.resource_name}: {e}")
            
    def disconnect(self) -> None:
        """Close VISA connection to the instrument."""
        try:
            if self.instrument is not None:
                self.instrument.close()
                self.instrument = None
                
            if self.resource_manager is not None:
                self.resource_manager.close()
                self.resource_manager = None
                
            self.is_connected = False
            logger.info(f"Disconnected from {self.resource_name}")
            
        except Exception as e:
            logger.warning(f"Error during disconnect from {self.resource_name}: {e}")
            
    def write(self, command: str) -> None:
        """
        Send a command to the instrument via VISA.
        
        Args:
            command: The SCPI command string to send
        """
        if not self.is_connected or self.instrument is None:
            raise ConnectionError("Adapter is not connected")
            
        try:
            self.instrument.write(command)
            logger.debug(f"Sent: {command}")
        except Exception as e:
            logger.error(f"Write error for command '{command}': {e}")
            raise
            
    def read(self) -> str:
        """
        Read a response from the instrument via VISA.
        
        Returns:
            The response string from the instrument
        """
        if not self.is_connected or self.instrument is None:
            raise ConnectionError("Adapter is not connected")
            
        try:
            response = self.instrument.read()
            logger.debug(f"Received: {response}")
            return response.strip()
        except Exception as e:
            logger.error(f"Read error: {e}")
            raise
            
    def query(self, command: str) -> str:
        """
        Send a command and read the response via VISA.
        
        Args:
            command: The SCPI command string to send
            
        Returns:
            The response string from the instrument
        """
        if not self.is_connected or self.instrument is None:
            raise ConnectionError("Adapter is not connected")
            
        try:
            response = self.instrument.query(command)
            logger.debug(f"Query '{command}' -> '{response}'")
            return response.strip()
        except Exception as e:
            logger.error(f"Query error for command '{command}': {e}")
            raise


class MockAdapter(Adapter):
    """
    Mock adapter for testing and development.
    
    This adapter simulates instrument communication without requiring
    actual hardware, useful for testing and development purposes.
    """
    
    def __init__(self, resource_name: str, **kwargs):
        """
        Initialize the mock adapter.
        
        Args:
            resource_name: Mock resource identifier
            **kwargs: Additional configuration parameters
        """
        super().__init__(resource_name, **kwargs)
        self.command_responses = {
            "*IDN?": "Mock Instrument,Model 1000,SN123456,v1.0",
            "*RST": "",
            "*CLS": "",
            "*OPC?": "1"
        }
        
    def connect(self) -> None:
        """Simulate connection establishment."""
        self.is_connected = True
        logger.info(f"Mock connection established to {self.resource_name}")
        
    def disconnect(self) -> None:
        """Simulate connection closure."""
        self.is_connected = False
        logger.info(f"Mock connection closed to {self.resource_name}")
        
    def write(self, command: str) -> None:
        """
        Simulate sending a command.
        
        Args:
            command: The command string to send
        """
        if not self.is_connected:
            raise ConnectionError("Mock adapter is not connected")
            
        logger.debug(f"Mock sent: {command}")
        
    def read(self) -> str:
        """
        Simulate reading a response.
        
        Returns:
            A mock response string
        """
        if not self.is_connected:
            raise ConnectionError("Mock adapter is not connected")
            
        response = "OK"
        logger.debug(f"Mock received: {response}")
        return response
        
    def query(self, command: str) -> str:
        """
        Simulate sending a command and reading the response.
        
        Args:
            command: The command string to send
            
        Returns:
            A mock response string
        """
        if not self.is_connected:
            raise ConnectionError("Mock adapter is not connected")
            
        # Return predefined response if available, otherwise generic response
        response = self.command_responses.get(command, f"MOCK_RESPONSE_FOR_{command}")
        logger.debug(f"Mock query '{command}' -> '{response}'")
        return response
        
    def add_response(self, command: str, response: str) -> None:
        """
        Add a custom response for a specific command.
        
        Args:
            command: The command to respond to
            response: The response to return
        """
        self.command_responses[command] = response


class Control:
    """
    Property descriptor for instrument control parameters.

    This descriptor provides a clean interface for mapping SCPI commands
    to Python properties, with automatic type conversion and validation.
    """

    def __init__(self, get_command: str = None, set_command: str = None,
                 doc: str = None, validator=None, values=None,
                 get_process=None, set_process=None, check_get_errors=False,
                 check_set_errors=False):
        """
        Initialize the control descriptor.

        Args:
            get_command: SCPI command to read the parameter
            set_command: SCPI command to set the parameter
            doc: Documentation string for the property
            validator: Function to validate input values
            values: List/dict of allowed values
            get_process: Function to process values read from instrument
            set_process: Function to process values before sending to instrument
            check_get_errors: Whether to check for errors after get operations
            check_set_errors: Whether to check for errors after set operations
        """
        self.get_command = get_command
        self.set_command = set_command
        self.__doc__ = doc
        self.validator = validator
        self.values = values
        self.get_process = get_process or (lambda x: x)
        self.set_process = set_process or (lambda x: x)
        self.check_get_errors = check_get_errors
        self.check_set_errors = check_set_errors

    def __get__(self, instance, owner):
        """Get the property value from the instrument."""
        if instance is None:
            return self

        if self.get_command is None:
            raise AttributeError("Property is write-only")

        try:
            response = instance.query(self.get_command)
            value = self.get_process(response)

            if self.check_get_errors:
                instance.check_errors()

            return value
        except Exception as e:
            logger.error(f"Error getting property with command '{self.get_command}': {e}")
            raise

    def __set__(self, instance, value):
        """Set the property value on the instrument."""
        if self.set_command is None:
            raise AttributeError("Property is read-only")

        # Validate value if validator is provided
        if self.validator is not None:
            self.validator(value)

        # Check against allowed values if provided
        if self.values is not None:
            if isinstance(self.values, dict):
                if value not in self.values.values() and value not in self.values.keys():
                    raise ValueError(f"Value {value} not in allowed values: {list(self.values.keys())}")
            elif hasattr(self.values, '__iter__'):
                if value not in self.values:
                    raise ValueError(f"Value {value} not in allowed values: {list(self.values)}")

        try:
            processed_value = self.set_process(value)
            command = self.set_command.format(processed_value)
            instance.write(command)

            if self.check_set_errors:
                instance.check_errors()

        except Exception as e:
            logger.error(f"Error setting property with command '{self.set_command}': {e}")
            raise


class Measurement:
    """
    Property descriptor for instrument measurements (read-only).

    This descriptor is similar to Control but only supports reading values
    from the instrument, making it suitable for measurement properties.
    """

    def __init__(self, get_command: str, doc: str = None,
                 get_process=None, check_errors=False):
        """
        Initialize the measurement descriptor.

        Args:
            get_command: SCPI command to read the measurement
            doc: Documentation string for the property
            get_process: Function to process values read from instrument
            check_errors: Whether to check for errors after operations
        """
        self.get_command = get_command
        self.__doc__ = doc
        self.get_process = get_process or (lambda x: x)
        self.check_errors = check_errors

    def __get__(self, instance, owner):
        """Get the measurement value from the instrument."""
        if instance is None:
            return self

        try:
            response = instance.query(self.get_command)
            value = self.get_process(response)

            if self.check_errors:
                instance.check_errors()

            return value
        except Exception as e:
            logger.error(f"Error getting measurement with command '{self.get_command}': {e}")
            raise

    def __set__(self, instance, value):
        """Measurements are read-only."""
        raise AttributeError("Measurement properties are read-only")


class Instrument:
    """
    Base class for all laboratory instruments.

    This class provides the foundation for instrument control, including
    communication through adapters and property-based parameter access.
    """

    # Class-level property descriptors
    control = Control
    measurement = Measurement

    def __init__(self, adapter: Union[Adapter, str], name: str = "Instrument", **kwargs):
        """
        Initialize the instrument.

        Args:
            adapter: Communication adapter instance or resource string
            name: Human-readable name for the instrument
            **kwargs: Additional configuration parameters
        """
        self.name = name

        # Create adapter if string is provided
        if isinstance(adapter, str):
            self.adapter = VISAAdapter(adapter, **kwargs)
        else:
            self.adapter = adapter

        # Connect to the instrument
        if not self.adapter.is_connected:
            self.adapter.connect()

        logger.info(f"Initialized instrument: {self.name}")

    def write(self, command: str) -> None:
        """
        Send a command to the instrument.

        Args:
            command: The command string to send
        """
        self.adapter.write(command)

    def read(self) -> str:
        """
        Read a response from the instrument.

        Returns:
            The response string from the instrument
        """
        return self.adapter.read()

    def query(self, command: str) -> str:
        """
        Send a command and read the response.

        Args:
            command: The command string to send

        Returns:
            The response string from the instrument
        """
        return self.adapter.query(command)

    def check_errors(self) -> None:
        """
        Check for instrument errors.

        This method should be overridden by specific instruments
        to implement error checking appropriate for that instrument.
        """
        pass

    def reset(self) -> None:
        """Reset the instrument to default state."""
        self.write("*RST")

    def clear(self) -> None:
        """Clear the instrument status."""
        self.write("*CLS")

    @property
    def id(self) -> str:
        """Get the instrument identification string."""
        return self.query("*IDN?")

    def close(self) -> None:
        """Close the connection to the instrument."""
        if self.adapter:
            self.adapter.close()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()

    def __repr__(self) -> str:
        """String representation of the instrument."""
        return f"{self.__class__.__name__}(name='{self.name}', adapter='{self.adapter.resource_name}')"
