"""
Dynamic instrument control widgets for laboratory automation framework.

This module provides widgets that automatically generate GUI controls
based on instrument properties and capabilities.
"""

import tkinter as tk
from tkinter import ttk
import inspect
from typing import Any, Dict, List, Callable, Optional
import logging

logger = logging.getLogger(__name__)


class InstrumentWidget:
    """
    Dynamic instrument control widget.
    
    This widget automatically generates GUI controls based on the
    properties and methods of an instrument object.
    """
    
    def __init__(self, parent, instrument, name: str = "Instrument"):
        """
        Initialize the instrument widget.
        
        Args:
            parent: Parent tkinter widget
            instrument: Instrument object to control
            name: Display name for the instrument
        """
        self.parent = parent
        self.instrument = instrument
        self.name = name
        
        # Storage for GUI elements
        self.controls = {}
        self.displays = {}
        self.buttons = {}
        
        # Update callbacks
        self.update_callbacks = []
        
        # Setup the widget
        self._setup_widget()
        
    def _setup_widget(self):
        """Setup the instrument control widget."""
        # Main frame
        self.main_frame = ttk.LabelFrame(self.parent, text=self.name)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create notebook for different sections
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Controls tab
        self.controls_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.controls_frame, text="Controls")
        
        # Status tab
        self.status_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.status_frame, text="Status")
        
        # Actions tab
        self.actions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.actions_frame, text="Actions")
        
        # Analyze instrument and create controls
        self._analyze_instrument()
        self._create_controls()
        self._create_status_displays()
        self._create_action_buttons()
        
        logger.info(f"Created instrument widget for {self.name}")
    
    def _analyze_instrument(self):
        """Analyze the instrument object to find controllable properties."""
        self.properties = {}
        self.methods = {}
        
        # Get all attributes
        for attr_name in dir(self.instrument):
            if attr_name.startswith('_'):
                continue
                
            attr = getattr(self.instrument, attr_name)
            
            # Check if it's a property (has getter/setter)
            if hasattr(self.instrument.__class__, attr_name):
                class_attr = getattr(self.instrument.__class__, attr_name)
                if isinstance(class_attr, property):
                    self.properties[attr_name] = {
                        'getter': class_attr.fget,
                        'setter': class_attr.fset,
                        'doc': class_attr.__doc__ or f"{attr_name} property"
                    }
            
            # Check if it's a callable method
            elif callable(attr) and not attr_name.startswith('_'):
                sig = inspect.signature(attr)
                self.methods[attr_name] = {
                    'method': attr,
                    'signature': sig,
                    'doc': attr.__doc__ or f"{attr_name} method"
                }
    
    def _create_controls(self):
        """Create control widgets for instrument properties."""
        row = 0
        
        for prop_name, prop_info in self.properties.items():
            if prop_info['setter'] is None:
                continue  # Read-only property
            
            # Create label
            label = ttk.Label(self.controls_frame, text=f"{prop_name}:")
            label.grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
            
            # Create control based on property type
            try:
                current_value = getattr(self.instrument, prop_name)
                control = self._create_control_for_value(
                    self.controls_frame, prop_name, current_value
                )
                control.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=2)
                
                # Create set button
                set_button = ttk.Button(
                    self.controls_frame,
                    text="Set",
                    command=lambda p=prop_name: self._set_property(p)
                )
                set_button.grid(row=row, column=2, padx=5, pady=2)
                
                self.controls[prop_name] = control
                
            except Exception as e:
                logger.warning(f"Could not create control for {prop_name}: {e}")
            
            row += 1
        
        # Configure column weights
        self.controls_frame.columnconfigure(1, weight=1)
    
    def _create_control_for_value(self, parent, prop_name: str, value: Any):
        """Create appropriate control widget based on value type."""
        if isinstance(value, bool):
            # Boolean: Checkbutton
            var = tk.BooleanVar(value=value)
            control = ttk.Checkbutton(parent, variable=var)
            control.var = var
            return control
            
        elif isinstance(value, (int, float)):
            # Numeric: Entry with validation
            var = tk.StringVar(value=str(value))
            control = ttk.Entry(parent, textvariable=var, width=15)
            control.var = var
            return control
            
        elif isinstance(value, str):
            # String: Entry
            var = tk.StringVar(value=value)
            control = ttk.Entry(parent, textvariable=var, width=20)
            control.var = var
            return control
            
        else:
            # Default: Entry with string representation
            var = tk.StringVar(value=str(value))
            control = ttk.Entry(parent, textvariable=var, width=20)
            control.var = var
            return control
    
    def _create_status_displays(self):
        """Create status display widgets for read-only properties."""
        row = 0
        
        for prop_name, prop_info in self.properties.items():
            # Create label
            label = ttk.Label(self.status_frame, text=f"{prop_name}:")
            label.grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
            
            # Create display
            var = tk.StringVar()
            display = ttk.Label(self.status_frame, textvariable=var, relief=tk.SUNKEN)
            display.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=2)
            
            # Create refresh button
            refresh_button = ttk.Button(
                self.status_frame,
                text="Refresh",
                command=lambda p=prop_name: self._refresh_property(p)
            )
            refresh_button.grid(row=row, column=2, padx=5, pady=2)
            
            self.displays[prop_name] = var
            
            # Initial update
            self._refresh_property(prop_name)
            
            row += 1
        
        # Configure column weights
        self.status_frame.columnconfigure(1, weight=1)
    
    def _create_action_buttons(self):
        """Create buttons for instrument methods."""
        row = 0
        col = 0
        max_cols = 3
        
        for method_name, method_info in self.methods.items():
            # Skip methods with complex signatures for now
            sig = method_info['signature']
            if len(sig.parameters) > 0:
                continue
            
            # Create button
            button = ttk.Button(
                self.actions_frame,
                text=method_name.replace('_', ' ').title(),
                command=lambda m=method_name: self._call_method(m)
            )
            button.grid(row=row, column=col, padx=5, pady=5, sticky=tk.EW)
            
            self.buttons[method_name] = button
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
        
        # Configure column weights
        for i in range(max_cols):
            self.actions_frame.columnconfigure(i, weight=1)
    
    def _set_property(self, prop_name: str):
        """Set a property value from the control widget."""
        try:
            control = self.controls[prop_name]
            
            # Get value from control
            if hasattr(control, 'var'):
                value_str = control.var.get()
            else:
                value_str = control.get()
            
            # Get current value to determine type
            current_value = getattr(self.instrument, prop_name)
            
            # Convert to appropriate type
            if isinstance(current_value, bool):
                value = bool(value_str) if isinstance(value_str, bool) else value_str.lower() in ('true', '1', 'yes', 'on')
            elif isinstance(current_value, int):
                value = int(float(value_str))  # Allow decimal input for int
            elif isinstance(current_value, float):
                value = float(value_str)
            else:
                value = value_str
            
            # Set the property
            setattr(self.instrument, prop_name, value)
            
            # Update status display
            self._refresh_property(prop_name)
            
            logger.info(f"Set {prop_name} = {value}")
            
        except Exception as e:
            logger.error(f"Error setting {prop_name}: {e}")
            # Show error in a popup or status bar
            self._show_error(f"Error setting {prop_name}: {e}")
    
    def _refresh_property(self, prop_name: str):
        """Refresh the display of a property value."""
        try:
            value = getattr(self.instrument, prop_name)
            display_var = self.displays[prop_name]
            display_var.set(str(value))
        except Exception as e:
            logger.error(f"Error refreshing {prop_name}: {e}")
            self.displays[prop_name].set(f"Error: {e}")
    
    def _call_method(self, method_name: str):
        """Call an instrument method."""
        try:
            method = self.methods[method_name]['method']
            result = method()
            
            logger.info(f"Called {method_name}(), result: {result}")
            
            # Refresh all displays after method call
            self.refresh_all()
            
        except Exception as e:
            logger.error(f"Error calling {method_name}: {e}")
            self._show_error(f"Error calling {method_name}: {e}")
    
    def _show_error(self, message: str):
        """Show error message to user."""
        # For now, just print to console
        # In a full GUI, this would show a popup dialog
        print(f"Error: {message}")
    
    def refresh_all(self):
        """Refresh all property displays."""
        for prop_name in self.displays:
            self._refresh_property(prop_name)
    
    def add_update_callback(self, callback: Callable):
        """Add a callback to be called when values are updated."""
        self.update_callbacks.append(callback)
    
    def remove_update_callback(self, callback: Callable):
        """Remove an update callback."""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)


class InstrumentPanel:
    """
    Panel containing multiple instrument widgets.
    
    This class manages multiple instruments in a single panel
    with tabs or sections for each instrument.
    """
    
    def __init__(self, parent):
        """
        Initialize the instrument panel.
        
        Args:
            parent: Parent tkinter widget
        """
        self.parent = parent
        self.instruments = {}
        self.widgets = {}
        
        # Setup the panel
        self._setup_panel()
    
    def _setup_panel(self):
        """Setup the instrument panel."""
        # Main frame
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Notebook for multiple instruments
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons
        self._setup_controls()
    
    def _setup_controls(self):
        """Setup control buttons for the panel."""
        control_frame = ttk.Frame(self.main_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Refresh all button
        refresh_button = ttk.Button(
            control_frame,
            text="Refresh All",
            command=self.refresh_all_instruments
        )
        refresh_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(control_frame, textvariable=self.status_var)
        status_label.pack(side=tk.RIGHT, padx=5)
    
    def add_instrument(self, name: str, instrument: Any):
        """
        Add an instrument to the panel.
        
        Args:
            name: Display name for the instrument
            instrument: Instrument object
        """
        # Create frame for this instrument
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=name)
        
        # Create instrument widget
        widget = InstrumentWidget(frame, instrument, name)
        
        # Store references
        self.instruments[name] = instrument
        self.widgets[name] = widget
        
        logger.info(f"Added instrument {name} to panel")
    
    def remove_instrument(self, name: str):
        """
        Remove an instrument from the panel.
        
        Args:
            name: Name of the instrument to remove
        """
        if name in self.instruments:
            # Find and remove the tab
            for i in range(self.notebook.index("end")):
                if self.notebook.tab(i, "text") == name:
                    self.notebook.forget(i)
                    break
            
            # Remove from storage
            del self.instruments[name]
            del self.widgets[name]
            
            logger.info(f"Removed instrument {name} from panel")
    
    def refresh_all_instruments(self):
        """Refresh all instrument displays."""
        for widget in self.widgets.values():
            widget.refresh_all()
        
        self.status_var.set("All instruments refreshed")
    
    def get_instrument(self, name: str) -> Any:
        """Get an instrument by name."""
        return self.instruments.get(name)
    
    def get_widget(self, name: str) -> InstrumentWidget:
        """Get an instrument widget by name."""
        return self.widgets.get(name)
