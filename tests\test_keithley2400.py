"""
Test script for <PERSON>ley 2400 SourceMeter driver.

This script tests the Keithley 2400 driver functionality using both
mock and real adapters.
"""

import sys
import os
import logging
import time

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from instruments.base import Mock<PERSON>dapter, VISAAdapter
from instruments.keithley2400 import Keithley2400

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_with_mock_adapter():
    """Test the Keithley 2400 driver with a mock adapter."""
    logger.info("Testing Keithley 2400 with mock adapter...")
    
    # Create mock adapter with some predefined responses
    mock_adapter = MockAdapter("MOCK::KEITHLEY2400::INSTR")
    mock_adapter.add_response("*IDN?", "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39")
    mock_adapter.add_response(":SOUR:VOLT:LEV?", "0.000000")
    mock_adapter.add_response(":SENS:CURR:PROT?", "1.050000E-03")
    mock_adapter.add_response(":OUTP?", "0")
    mock_adapter.add_response(":SOUR:FUNC?", "VOLT")
    mock_adapter.add_response(":MEAS:VOLT?", "1.234567E+00")
    mock_adapter.add_response(":MEAS:CURR?", "5.678901E-04")
    mock_adapter.add_response(":READ?", "1.234567E+00,5.678901E-04,9.999910E+37")
    mock_adapter.add_response(":SYST:ERR?", "0,\"No error\"")
    
    try:
        # Create instrument instance
        smu = Keithley2400(mock_adapter, name="Mock K2400")
        
        # Test basic properties
        logger.info(f"Instrument ID: {smu.id}")
        logger.info(f"Source voltage: {smu.source_voltage} V")
        logger.info(f"Compliance current: {smu.compliance_current} A")
        logger.info(f"Output enabled: {smu.source_enabled}")
        logger.info(f"Source function: {smu.source_function}")
        
        # Test setting properties
        smu.source_voltage = 2.5
        logger.info("Set source voltage to 2.5V")
        
        smu.compliance_current = 0.01
        logger.info("Set compliance current to 10mA")
        
        smu.source_enabled = True
        logger.info("Enabled output")
        
        # Test measurements
        voltage = smu.voltage
        current = smu.current
        logger.info(f"Measured voltage: {voltage} V")
        logger.info(f"Measured current: {current} A")
        
        # Test I-V measurement
        v, i = smu.measure_iv()
        logger.info(f"I-V measurement: {v} V, {i} A")
        
        # Test configuration methods
        smu.configure_voltage_source(1.0, 0.001)
        smu.configure_current_source(0.001, 10.0)
        
        # Test utility methods
        smu.auto_range_current(True)
        smu.auto_range_voltage(True)
        smu.beep(1000, 0.2)
        
        # Check for errors
        error = smu.check_errors()
        if error:
            logger.warning(f"Instrument error: {error}")
        else:
            logger.info("No instrument errors")
        
        # Clean up
        smu.source_enabled = False
        smu.close()
        
        logger.info("Mock adapter test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Mock adapter test failed: {e}")
        return False


def test_with_visa_adapter():
    """Test the Keithley 2400 driver with a real VISA adapter."""
    logger.info("Testing Keithley 2400 with VISA adapter...")
    
    # Try common GPIB addresses for Keithley instruments
    addresses = [
        "GPIB0::24::INSTR",  # Common default address
        "GPIB0::1::INSTR",   # Alternative address
        "USB0::0x05E6::0x2400::1234567::INSTR",  # USB connection example
    ]
    
    for address in addresses:
        try:
            logger.info(f"Trying to connect to {address}...")
            
            # Create VISA adapter
            visa_adapter = VISAAdapter(address, timeout=5000)
            
            # Create instrument instance
            smu = Keithley2400(visa_adapter, name="Real K2400")
            
            # Test basic communication
            logger.info(f"Connected! Instrument ID: {smu.id}")
            
            # Test reading current state
            logger.info(f"Current source voltage: {smu.source_voltage} V")
            logger.info(f"Current compliance: {smu.compliance_current} A")
            logger.info(f"Output enabled: {smu.source_enabled}")
            
            # Perform a safe test measurement (low voltage, low current limit)
            logger.info("Performing safe test measurement...")
            smu.configure_voltage_source(0.1, 0.001)  # 100mV, 1mA limit
            
            # Enable output briefly for measurement
            smu.source_enabled = True
            time.sleep(0.1)  # Brief settling time
            
            # Take measurement
            v, i = smu.measure_iv()
            logger.info(f"Test measurement: {v} V, {i} A")
            
            # Disable output
            smu.source_enabled = False
            
            # Check for errors
            error = smu.check_errors()
            if error:
                logger.warning(f"Instrument error: {error}")
            
            # Clean up
            smu.close()
            
            logger.info("VISA adapter test completed successfully!")
            return True
            
        except Exception as e:
            logger.warning(f"Failed to connect to {address}: {e}")
            continue
    
    logger.warning("Could not connect to any Keithley 2400 instrument")
    return False


def run_interactive_test():
    """Run an interactive test allowing user to choose test mode."""
    print("\nKeithley 2400 Driver Test")
    print("=" * 40)
    print("1. Test with mock adapter (no hardware required)")
    print("2. Test with real VISA adapter (requires hardware)")
    print("3. Run both tests")
    print("0. Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (0-3): ").strip()
            
            if choice == "0":
                print("Exiting...")
                break
            elif choice == "1":
                test_with_mock_adapter()
            elif choice == "2":
                test_with_visa_adapter()
            elif choice == "3":
                test_with_mock_adapter()
                test_with_visa_adapter()
            else:
                print("Invalid choice. Please enter 0, 1, 2, or 3.")
                
        except KeyboardInterrupt:
            print("\nTest interrupted by user.")
            break
        except Exception as e:
            logger.error(f"Unexpected error: {e}")


if __name__ == "__main__":
    # Check if running in interactive mode
    if len(sys.argv) > 1:
        if sys.argv[1] == "mock":
            test_with_mock_adapter()
        elif sys.argv[1] == "visa":
            test_with_visa_adapter()
        elif sys.argv[1] == "both":
            test_with_mock_adapter()
            test_with_visa_adapter()
        else:
            print("Usage: python test_keithley2400.py [mock|visa|both]")
    else:
        run_interactive_test()
