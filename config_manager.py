"""
Configuration manager for laboratory automation framework.

This module handles loading, validating, and managing configuration
for instruments, procedures, and framework settings.
"""

import json
import os
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# Import instrument classes
from instruments.base import VI<PERSON><PERSON><PERSON>pter, MockAdapter
from instruments.keithley2400 import Keithley2400

logger = logging.getLogger(__name__)


class ConfigurationManager:
    """
    Configuration manager for the laboratory automation framework.
    
    This class handles loading configuration from JSON files and
    creating instrument instances based on the configuration.
    """
    
    def __init__(self, config_file: str = "config.json"):
        """
        Initialize the configuration manager.
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.config = {}
        self.instruments = {}
        self.procedures = {}
        
        # Instrument type registry
        self.instrument_types = {
            "Keithley2400": Keithley2400,
        }
        
        # Adapter type registry
        self.adapter_types = {
            "VISAAdapter": VISAAdapter,
            "MockAdapter": <PERSON><PERSON><PERSON><PERSON>pt<PERSON>,
        }
        
        # Load configuration
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file."""
        try:
            if not os.path.exists(self.config_file):
                logger.warning(f"Configuration file {self.config_file} not found, using defaults")
                self._create_default_config()
                return
            
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
            
            logger.info(f"Configuration loaded from {self.config_file}")
            self._validate_config()
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self._create_default_config()
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            
            logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def _create_default_config(self) -> None:
        """Create default configuration."""
        self.config = {
            "framework": {
                "name": "Laboratory Automation Framework",
                "version": "0.1.0",
                "log_level": "INFO"
            },
            "instruments": {},
            "procedures": {},
            "gui": {
                "theme": "default",
                "window_size": {"width": 1200, "height": 800}
            },
            "data": {
                "output_directory": "data",
                "file_format": "csv"
            }
        }
        logger.info("Created default configuration")
    
    def _validate_config(self) -> None:
        """Validate the loaded configuration."""
        required_sections = ["framework", "instruments", "procedures", "gui", "data"]
        
        for section in required_sections:
            if section not in self.config:
                logger.warning(f"Missing configuration section: {section}")
                self.config[section] = {}
    
    def get_framework_config(self) -> Dict[str, Any]:
        """Get framework configuration."""
        return self.config.get("framework", {})
    
    def get_instrument_config(self, instrument_name: str) -> Optional[Dict[str, Any]]:
        """
        Get configuration for a specific instrument.
        
        Args:
            instrument_name: Name of the instrument
            
        Returns:
            Instrument configuration or None if not found
        """
        return self.config.get("instruments", {}).get(instrument_name)
    
    def get_all_instrument_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all instrument configurations."""
        return self.config.get("instruments", {})
    
    def get_procedure_config(self, procedure_name: str) -> Optional[Dict[str, Any]]:
        """
        Get configuration for a specific procedure.
        
        Args:
            procedure_name: Name of the procedure
            
        Returns:
            Procedure configuration or None if not found
        """
        return self.config.get("procedures", {}).get(procedure_name)
    
    def get_all_procedure_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all procedure configurations."""
        return self.config.get("procedures", {})
    
    def get_gui_config(self) -> Dict[str, Any]:
        """Get GUI configuration."""
        return self.config.get("gui", {})
    
    def get_data_config(self) -> Dict[str, Any]:
        """Get data configuration."""
        return self.config.get("data", {})
    
    def create_adapter(self, adapter_config: Dict[str, Any]) -> Any:
        """
        Create an adapter instance from configuration.
        
        Args:
            adapter_config: Adapter configuration dictionary
            
        Returns:
            Adapter instance
        """
        adapter_type = adapter_config.get("type")
        if adapter_type not in self.adapter_types:
            raise ValueError(f"Unknown adapter type: {adapter_type}")
        
        adapter_class = self.adapter_types[adapter_type]
        resource = adapter_config.get("resource")
        
        # Extract additional parameters
        kwargs = {k: v for k, v in adapter_config.items() 
                 if k not in ["type", "resource"]}
        
        adapter = adapter_class(resource, **kwargs)

        # Add default responses for mock adapters
        if adapter_type == "MockAdapter":
            self._setup_mock_responses(adapter, adapter_config)

        return adapter
    
    def create_instrument(self, instrument_name: str, instrument_config: Dict[str, Any]) -> Any:
        """
        Create an instrument instance from configuration.
        
        Args:
            instrument_name: Name of the instrument
            instrument_config: Instrument configuration dictionary
            
        Returns:
            Instrument instance
        """
        instrument_type = instrument_config.get("type")
        if instrument_type not in self.instrument_types:
            raise ValueError(f"Unknown instrument type: {instrument_type}")
        
        # Create adapter
        adapter_config = instrument_config.get("adapter", {})
        adapter = self.create_adapter(adapter_config)
        
        # Create instrument
        instrument_class = self.instrument_types[instrument_type]
        display_name = instrument_config.get("name", instrument_name)
        
        instrument = instrument_class(adapter, name=display_name)
        
        # Apply settings if provided
        settings = instrument_config.get("settings", {})
        self._apply_instrument_settings(instrument, settings)
        
        return instrument
    
    def _apply_instrument_settings(self, instrument: Any, settings: Dict[str, Any]) -> None:
        """
        Apply settings to an instrument.
        
        Args:
            instrument: Instrument instance
            settings: Settings dictionary
        """
        for setting_name, setting_value in settings.items():
            try:
                if hasattr(instrument, setting_name):
                    setattr(instrument, setting_name, setting_value)
                    logger.debug(f"Applied setting {setting_name} = {setting_value}")
                else:
                    logger.warning(f"Unknown setting for instrument: {setting_name}")
            except Exception as e:
                logger.error(f"Error applying setting {setting_name}: {e}")
    
    def load_instruments(self, auto_connect: bool = True) -> Dict[str, Any]:
        """
        Load all configured instruments.
        
        Args:
            auto_connect: Whether to automatically connect to instruments
            
        Returns:
            Dictionary of instrument instances
        """
        instruments = {}
        instrument_configs = self.get_all_instrument_configs()
        
        for instrument_name, config in instrument_configs.items():
            try:
                # Check if instrument is enabled
                if not config.get("enabled", True):
                    logger.info(f"Skipping disabled instrument: {instrument_name}")
                    continue
                
                # Check auto_connect setting
                if auto_connect and not config.get("auto_connect", True):
                    logger.info(f"Skipping auto-connect for instrument: {instrument_name}")
                    continue
                
                # Create instrument
                instrument = self.create_instrument(instrument_name, config)
                instruments[instrument_name] = instrument
                
                logger.info(f"Loaded instrument: {instrument_name}")
                
            except Exception as e:
                logger.error(f"Failed to load instrument {instrument_name}: {e}")
        
        self.instruments = instruments
        return instruments
    
    def get_enabled_instruments(self) -> List[str]:
        """Get list of enabled instrument names."""
        enabled = []
        for name, config in self.get_all_instrument_configs().items():
            if config.get("enabled", True):
                enabled.append(name)
        return enabled
    
    def get_enabled_procedures(self) -> List[str]:
        """Get list of enabled procedure names."""
        enabled = []
        for name, config in self.get_all_procedure_configs().items():
            if config.get("enabled", True):
                enabled.append(name)
        return enabled
    
    def update_instrument_config(self, instrument_name: str, config: Dict[str, Any]) -> None:
        """
        Update configuration for an instrument.
        
        Args:
            instrument_name: Name of the instrument
            config: New configuration
        """
        if "instruments" not in self.config:
            self.config["instruments"] = {}
        
        self.config["instruments"][instrument_name] = config
        logger.info(f"Updated configuration for instrument: {instrument_name}")
    
    def update_procedure_config(self, procedure_name: str, config: Dict[str, Any]) -> None:
        """
        Update configuration for a procedure.
        
        Args:
            procedure_name: Name of the procedure
            config: New configuration
        """
        if "procedures" not in self.config:
            self.config["procedures"] = {}
        
        self.config["procedures"][procedure_name] = config
        logger.info(f"Updated configuration for procedure: {procedure_name}")
    
    def close_all_instruments(self) -> None:
        """Close all loaded instruments."""
        for name, instrument in self.instruments.items():
            try:
                instrument.close()
                logger.info(f"Closed instrument: {name}")
            except Exception as e:
                logger.error(f"Error closing instrument {name}: {e}")
        
        self.instruments.clear()

    def _setup_mock_responses(self, adapter: MockAdapter, adapter_config: Dict[str, Any]) -> None:
        """
        Setup default responses for mock adapters.

        Args:
            adapter: Mock adapter instance
            adapter_config: Adapter configuration
        """
        # Default SCPI responses for Keithley 2400
        default_responses = {
            "*IDN?": "KEITHLEY INSTRUMENTS INC.,MODEL 2400,1234567,C30 Mar 17 1999 09:32:39",
            "*RST": "",
            "*CLS": "",
            "*OPC?": "1",
            ":SOUR:VOLT:LEV?": "0.000000",
            ":SENS:CURR:PROT?": "1.050000E-03",
            ":SENS:VOLT:PROT?": "21.000000",
            ":SOUR:CURR:LEV?": "0.000000E+00",
            ":OUTP?": "0",
            ":SOUR:FUNC?": "VOLT",
            ":SENS:CURR:RANG?": "1.050000E-03",
            ":SENS:VOLT:RANG?": "21.000000",
            ":MEAS:VOLT?": "0.000000",
            ":MEAS:CURR?": "0.000000E+00",
            ":MEAS:RES?": "9.999910E+37",
            ":READ?": "0.000000,0.000000E+00,9.999910E+37",
            ":SYST:ERR?": "0,\"No error\""
        }

        # Add custom responses from config if provided
        custom_responses = adapter_config.get("responses", {})
        default_responses.update(custom_responses)

        # Add all responses to the adapter
        for command, response in default_responses.items():
            adapter.add_response(command, response)


# Global configuration manager instance
config_manager = ConfigurationManager()


def get_config_manager() -> ConfigurationManager:
    """Get the global configuration manager instance."""
    return config_manager
